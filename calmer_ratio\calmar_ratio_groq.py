import pandas as pd
import numpy as np
import datetime as dt
import csv
from backtesting import Backtest, Strategy
from backtesting.lib import crossover
from backtesting.test import SMA

def BB(array, n, std_dev):
    """Calculate Bollinger Bands for an array."""
    # Calculate the middle band (SMA)
    middle = pd.Series(array).rolling(n).mean().values
    
    # Calculate the standard deviation
    std = pd.Series(array).rolling(n).std().values
    
    # Calculate the upper and lower bands
    upper = middle + std_dev * std
    lower = middle - std_dev * std
    
    return upper, middle, lower

def is_time_between(time_str, start_time_str, end_time_str):
    """Check if a time string is between start and end time strings."""
    time = dt.datetime.strptime(time_str, "%H:%M").time()
    start_time = dt.datetime.strptime(start_time_str, "%H:%M").time()
    end_time = dt.datetime.strptime(end_time_str, "%H:%M").time()
    return start_time <= time <= end_time

class MeanReversionStrategy(Strategy):
    short_window = 5
    long_window = 20
    bb_window = 20
    bb_std = 2
    
    # Time constraints
    market_start_time = "09:15"
    market_end_time = "15:30"
    entry_start_time = "09:30"
    exit_end_time = "15:15"  # force close all trades
    
    # Trade tracking
    trade_records = []
    current_trade = None

    def init(self):
        # Calculate indicators
        self.short_ma = self.I(SMA, self.data.Close, self.short_window)
        self.long_ma = self.I(SMA, self.data.Close, self.long_window)
        
        # Calculate Bollinger Bands
        self.bb_upper, self.bb_mid, self.bb_lower = self.I(BB, self.data.Close, self.bb_window, self.bb_std)
        
        # Initialize trade tracking
        self.trade_records = []
        self.current_trade = None

    def next(self):
        # Skip trading until we have enough data
        if np.isnan(self.bb_lower[-1]) or np.isnan(self.bb_upper[-1]) or np.isnan(self.short_ma[-1]) or np.isnan(self.long_ma[-1]):
            return
        
        # Get current time
        current_time = self.data.index[-1].strftime("%H:%M")
        current_datetime = self.data.index[-1]
        
        # Force close all positions at exit_end_time
        if is_time_between(current_time, self.exit_end_time, self.market_end_time) and self.position:
            # Store exit reason before closing
            if self.current_trade:
                self.current_trade["exit_reason"] = "EOD_CLOSE"
            if self.position.is_long or self.position.is_short:
                self.position.close()
            return
        
        # Only enter new positions between entry_start_time and exit_end_time
        if not is_time_between(current_time, self.entry_start_time, self.exit_end_time):
            return
        
        # Trading logic
        if self.short_ma[-1] > self.bb_lower[-1] and self.data.Close[-1] < self.long_ma[-1] and not self.position:
            # Long entry
            self.buy(size=1)
            self.current_trade = {
                "entry_datetime": current_datetime,
                "entry_price": self.data.Close[-1],
                "position": "LONG",
                "exit_reason": "OPEN"  # Initialize exit reason
            }
        elif self.short_ma[-1] < self.bb_upper[-1] and self.data.Close[-1] > self.long_ma[-1] and not self.position:
            # Short entry
            self.sell(size=1)
            self.current_trade = {
                "entry_datetime": current_datetime,
                "entry_price": self.data.Close[-1],
                "position": "SHORT",
                "exit_reason": "OPEN"  # Initialize exit reason
            }
        
        # Exit conditions for existing positions
        if self.position:
            # Exit long position
            if self.position.is_long and (self.short_ma[-1] < self.bb_upper[-1] or self.data.Close[-1] > self.long_ma[-1]):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "SIGNAL_EXIT"
                self.position.close()
            # Exit short position
            elif self.position.is_short and (self.short_ma[-1] > self.bb_lower[-1] or self.data.Close[-1] < self.long_ma[-1]):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "SIGNAL_EXIT"
                self.position.close()
    
    def on_trade(self, trade):
        """Called when a trade is closed."""
        if self.current_trade is not None:
            # Calculate trade metrics
            exit_datetime = self.data.index[-1]
            exit_price = trade.exit_price
            exit_reason = self.current_trade.get("exit_reason", "UNKNOWN")
            
            # Calculate profit
            profit_points = exit_price - self.current_trade["entry_price"] if self.current_trade["position"] == "LONG" else self.current_trade["entry_price"] - exit_price
            profit_percent = (profit_points / self.current_trade["entry_price"]) * 100
            
            # Calculate trade duration
            duration = exit_datetime - self.current_trade["entry_datetime"]
            duration_str = str(duration)
            
            # Create trade record
            trade_record = {
                "index": len(self.trade_records) + 1,
                "entry_datetime": self.current_trade["entry_datetime"],
                "exit_datetime": exit_datetime,
                "entry_price": self.current_trade["entry_price"],
                "exit_price": exit_price,
                "exit_reason": exit_reason,
                "profit_points": profit_points,
                "profit_percent": profit_percent,
                "position": self.current_trade["position"],
                "trade_duration": duration_str,
                "exit_reason": exit_reason
            }
            
            # Add to trade records
            self.trade_records.append(trade_record)
            self.current_trade = None
# --- Main Execution ---
if __name__ == '__main__':
    # Load the data
    file_path = 'C:\\Users\\<USER>\\Desktop\\Python\\data\\NIFTY_1min.csv'
    try:
        data = pd.read_csv(
            file_path,
            parse_dates=['date'],
            index_col='date'
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming)
    # backtesting.py expects: 'Open', 'High', 'Low', 'Close', 'Volume'
    column_map_lower = {col.lower(): col for col in data.columns}
    
    rename_dict = {}
    # Map standard OHLC names first
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        else:
            # Check if the correctly cased 'bt_name' (e.g. 'Open') already exists
            if bt_name not in data.columns:
                 print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
                 exit()
            
    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present
    if 'volume' in column_map_lower:
        # If 'volume' (lowercase) exists, rename its original cased version to 'Volume'
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns: # Check if 'Volume' (exact case) is already present
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0
    
    # Verify final column names
    expected_bt_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns for backtesting.py are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")
    
    # Filter data for a smaller period for faster testing
    # Use just 1 month of data for testing
    data = data.loc['2015-01-01':'2015-02-01']
    print(f"Data filtered for testing. New shape: {data.shape}")
    
    # Create Backtest with much higher cash to handle the large dataset
    bt = Backtest(data, MeanReversionStrategy, cash=50000, commission=0.0)
    stats = bt.run()

    # Print the stats
    print(stats)
    
    # Calculate Calmar Ratio manually
    # Get equity curve from stats
    equity = stats['_equity_curve']['Equity']
    
    # Calculate returns
    returns = equity.pct_change().dropna()
    
    # Calculate drawdown
    drawdown = 1 - equity / equity.cummax()
    max_drawdown = drawdown.max()
    
    # Calculate Calmar Ratio (annualized return / max drawdown)
    annual_return = returns.mean() * 252  # Assuming 252 trading days in a year
    calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else float('inf')

    print(f"Calmar Ratio: {calmar_ratio}")
    
    # Extract trade information from the backtest results
    trades_df = stats['_trades']
    
    if not trades_df.empty:
        # Process trades to create our custom format
        output_file = 'C:\\Users\\<USER>\\Desktop\\Python\\SimpleStrategies\\trade_records.csv'
        
        # Create a list to store processed trade records
        trade_records = []
        
        # Process each trade
        for idx, trade in trades_df.iterrows():
            # Determine position type (LONG/SHORT)
            position_type = "LONG" if trade['Size'] > 0 else "SHORT"
            
            # Calculate profit points and percent
            entry_price = trade['EntryPrice']
            exit_price = trade['ExitPrice']
            
            if position_type == "LONG":
                profit_points = exit_price - entry_price
            else:
                profit_points = entry_price - exit_price
                
            profit_percent = (profit_points / entry_price) * 100
            
            # Calculate trade duration
            entry_time = trade['EntryTime']
            exit_time = trade['ExitTime']
            duration = exit_time - entry_time
            
            # Determine exit reason (simplified)
            if exit_time.strftime("%H:%M") >= "15:15":
                exit_reason = "EOD_CLOSE"
            else:
                exit_reason = "SIGNAL_EXIT"
            
            # Create trade record
            trade_record = {
                'Index': idx + 1,
                'Entry DateTime': entry_time,
                'Exit DateTime': exit_time,
                'Entry Price': entry_price,
                'Exit Price': exit_price,
                'Exit Reason': exit_reason,
                'Profit Points': profit_points,
                'Profit Percent': profit_percent,
                'Position': position_type,
                'Trade Duration': str(duration),
                'Exit Reason': exit_reason  # Duplicate field as requested
            }
            
            trade_records.append(trade_record)
        
        # Define CSV headers
        headers = [
            'Index', 'Entry DateTime', 'Exit DateTime', 'Entry Price', 'Exit Price',
            'Exit Reason', 'Profit Points', 'Profit Percent', 'Position', 'Trade Duration', 'Exit Reason'
        ]
        
        # Write to CSV
        with open(output_file, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            
            for trade in trade_records:
                writer.writerow(trade)
        
        print(f"Saved {len(trade_records)} trade records to {output_file}")
    else:
        print("No trades were executed during the backtest.")