import pandas as pd
import numpy as np
from backtesting import Strategy, Backtest

# Define RSI calculation function
def calculate_rsi(series, period=14):
    """
    Calculate the Relative Strength Index (RSI) for a given series.

    Args:
        series: Price series (usually close prices)
        period: RSI period (default: 14)

    Returns:
        RSI values as a pandas Series
    """
    series = pd.Series(series)
    delta = series.diff()

    # Make two series: one for gains, one for losses
    gain = delta.mask(delta < 0, 0)
    loss = -delta.mask(delta > 0, 0)

    # Calculate average gain and loss
    avg_gain = gain.rolling(window=period, min_periods=1).mean()
    avg_loss = loss.rolling(window=period, min_periods=1).mean()

    # Calculate RS
    rs = avg_gain / avg_loss.replace(0, np.finfo(float).eps)  # Avoid division by zero

    # Calculate RSI
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_sma(series, period):
    series = pd.Series(series)
    return series.rolling(period, min_periods=1).mean()

def calculate_atr(high, low, close, period):
    high = pd.Series(high)
    low = pd.Series(low)
    close = pd.Series(close)

    tr = pd.DataFrame(index=high.index)
    tr['hl'] = high - low
    tr['hc'] = abs(high - close.shift(1))
    tr['lc'] = abs(low - close.shift(1))
    true_range = tr[['hl', 'hc', 'lc']].max(axis=1)
    return true_range.ewm(span=period, adjust=False, min_periods=1).mean()

# Assuming you've named your class SupportResistanceBreakoutRetestImprovedCSV
class SupportResistanceBreakoutRetestImprovedCSV(Strategy):
    # Configuration
    n_periods = 50
    retest_window = 3
    confirmation_lookback = 1
    sl_atr_multiplier = 1.5
    tp_rr = 2
    trend_ma_period = 100
    min_breakout_body_percent = 0.5
    sr_band_percent = 0.001
    debug_mode = True  # Set to True to enable detailed debugging output

    # RSI parameters
    rsi_period = 14
    rsi_oversold = 30
    rsi_overbought = 70

    # Time constraints
    market_start_time = "09:15"
    market_end_time = "15:30"
    entry_start_time = "09:15"  # Allow time for indicators to stabilize
    exit_end_time = "15:15"     # Force close all trades before market close

    # Enhanced Take Profit Configuration
    extended_breakout_window = 20  # Extended window for trades to develop
    dynamic_tp_enabled = True      # Enable dynamic take profit based on volatility
    trailing_stop_enabled = True   # Enable trailing stop functionality
    partial_tp_enabled = True      # Enable partial take profit
    partial_tp_ratio = 0.5         # Take 50% profit at 1:1 R:R
    trailing_stop_trigger = 1.0    # Start trailing when 1:1 R:R is reached
    trailing_stop_distance = 0.5   # Trail by 0.5x ATR

    # Volatility-based TP adjustment
    high_vol_tp_multiplier = 1.5   # Reduce TP in high volatility
    low_vol_tp_multiplier = 2.5    # Increase TP in low volatility
    vol_threshold = 1.2            # ATR ratio threshold for volatility classification

    def init(self):
        self.resistance = self.I(lambda x: pd.Series(x).rolling(self.n_periods, min_periods=1).max(), self.data.High, name="resistance")
        self.support = self.I(lambda x: pd.Series(x).rolling(self.n_periods, min_periods=1).min(), self.data.Low, name="support")
        self.atr = self.I(calculate_atr, self.data.High, self.data.Low, self.data.Close, 14, name="atr")
        self.trend_ma = self.I(calculate_sma, self.data.Close, self.trend_ma_period, name="trend_ma")

        # Add RSI indicator for trend reversal detection
        self.rsi = self.I(calculate_rsi, self.data.Close, self.rsi_period, name="rsi")

        # Track support and resistance levels for detecting changes
        self.support_level = self.I(lambda x: pd.Series(x).rolling(self.n_periods, min_periods=1).min(), self.data.Low, name="support_level")
        self.resistance_level = self.I(lambda x: pd.Series(x).rolling(self.n_periods, min_periods=1).max(), self.data.High, name="resistance_level")

        # Variables for trade logging
        self.trade_log = []
        self.current_trade_entry_details = {} # To temporarily store entry info
        self.trade_index_counter = 1 # To assign unique index to each trade

        # Existing strategy variables
        self.breakout_up_level = np.nan
        self.breakout_down_level = np.nan
        self.breakout_candle_idx = -1
        self.retest_touch_candle_idx = -1
        self.retest_touch_candle_high = np.nan
        self.retest_touch_candle_low = np.nan
        self.waiting_for_confirmation = False
        self.breakout_direction = 0

        # Initialize variables for tracking trading days and overnight positions
        self.last_processed_date = None

        # Enhanced take profit tracking variables
        self.trailing_stop_price = np.nan
        self.trailing_stop_active = False
        self.partial_tp_taken = False
        self.original_position_size = 0
        self.highest_favorable_price = np.nan  # Track highest price for long, lowest for short

    def next(self):
        current_idx = len(self.data.Close) - 1
        current_datetime = self.data.index[-1] # Get current bar's datetime
        current_close = self.data.Close[-1]
        current_open = self.data.Open[-1]
        current_high = self.data.High[-1]
        current_low = self.data.Low[-1]

        # Get current time in HH:MM format
        current_time = current_datetime.strftime('%H:%M')

        # Get current date in YYYY-MM-DD format
        current_date = current_datetime.strftime('%Y-%m-%d')

        # Check for new trading day - handle overnight positions
        if self.last_processed_date is not None and current_date != self.last_processed_date:
            if self.position and self.current_trade_entry_details:
                if self.debug_mode:
                    print(f"DEBUG - OVERNIGHT POSITION DETECTED: Date change from {self.last_processed_date} to {current_date}")
                    print(f"DEBUG - Closing overnight position at market open")

                # Force close any overnight positions
                self.current_trade_entry_details["Exit Reason"] = "Overnight Exit"
                self.position.close()
                self._record_exit_details(current_datetime, current_close)
                self.current_trade_entry_details = {}

        # Update last processed date
        self.last_processed_date = current_date

        # --- Enhanced Stop Loss and Take Profit Check ---
        # This checks if the price has moved beyond SL/TP during the candle, not just at close
        if self.position and self.current_trade_entry_details:
            position_type = self.current_trade_entry_details["Position"]
            sl_price = self.current_trade_entry_details["SL Price"]
            tp_price = self.current_trade_entry_details["TP Price"]
            entry_price = self.current_trade_entry_details["Entry Price"]

            # For LONG positions
            if position_type == "LONG":
                # Update highest favorable price for trailing stop
                if np.isnan(self.highest_favorable_price) or current_high > self.highest_favorable_price:
                    self.highest_favorable_price = current_high

                # Debug logging
                if self.debug_mode:
                    print(f"DEBUG - LONG Position Check: Time: {current_time}, Low: {current_low}, High: {current_high}, Close: {current_close}")
                    print(f"DEBUG - LONG SL/TP Levels: SL: {sl_price}, TP: {tp_price}, Entry: {entry_price}")
                    print(f"DEBUG - LONG Highest Price: {self.highest_favorable_price}, Trailing Active: {self.trailing_stop_active}")

                # Check for trailing stop first (highest priority for profit protection)
                if self.trailing_stop_active and not np.isnan(self.trailing_stop_price):
                    if current_low <= self.trailing_stop_price:
                        self.current_trade_entry_details["Exit Reason"] = "Trailing Stop"
                        if self.debug_mode:
                            print(f"DEBUG - LONG TRAILING STOP TRIGGERED: Low: {current_low}, Trailing SL: {self.trailing_stop_price}")
                        self.position.close()
                        self._record_exit_details(current_datetime, self.trailing_stop_price)
                        self._reset_trade_variables()
                        return

                # Check if low price touched or went below stop loss
                elif current_low <= sl_price:
                    self.current_trade_entry_details["Exit Reason"] = "Stop Loss"
                    if self.debug_mode:
                        print(f"DEBUG - LONG STOP LOSS TRIGGERED: Low: {current_low}, SL: {sl_price}")
                    self.position.close()
                    self._record_exit_details(current_datetime, sl_price)
                    self._reset_trade_variables()
                    return

                # Check for partial take profit at 1:1 R:R
                elif self.partial_tp_enabled and not self.partial_tp_taken:
                    risk_points = entry_price - sl_price
                    partial_tp_level = entry_price + risk_points  # 1:1 R:R
                    if current_high >= partial_tp_level:
                        if self.debug_mode:
                            print(f"DEBUG - LONG PARTIAL TP TRIGGERED: High: {current_high}, Partial TP: {partial_tp_level}")
                        # Take partial profit but keep position open
                        self.partial_tp_taken = True
                        # Note: In backtesting framework, we can't actually take partial profits
                        # So we'll just mark it and continue to full TP

                # Check if high price touched or exceeded take profit
                elif current_high >= tp_price:
                    self.current_trade_entry_details["Exit Reason"] = "Take Profit"
                    if self.debug_mode:
                        print(f"DEBUG - LONG TAKE PROFIT TRIGGERED: High: {current_high}, TP: {tp_price}")
                    self.position.close()
                    self._record_exit_details(current_datetime, tp_price)
                    self._reset_trade_variables()
                    return

                # Activate trailing stop when profit reaches trigger level
                elif self.trailing_stop_enabled and not self.trailing_stop_active:
                    risk_points = entry_price - sl_price
                    trailing_trigger_level = entry_price + (risk_points * self.trailing_stop_trigger)
                    if current_high >= trailing_trigger_level:
                        self.trailing_stop_active = True
                        self.trailing_stop_price = current_high - (self.atr[-1] * self.trailing_stop_distance)
                        if self.debug_mode:
                            print(f"DEBUG - LONG TRAILING STOP ACTIVATED: Trigger: {trailing_trigger_level}, Initial Trail: {self.trailing_stop_price}")

                # Update trailing stop if active
                elif self.trailing_stop_active:
                    new_trailing_stop = self.highest_favorable_price - (self.atr[-1] * self.trailing_stop_distance)
                    if new_trailing_stop > self.trailing_stop_price:
                        self.trailing_stop_price = new_trailing_stop
                        if self.debug_mode:
                            print(f"DEBUG - LONG TRAILING STOP UPDATED: New Trail: {self.trailing_stop_price}")
                # Check for trend reversal
                elif self.rsi[-1] < self.rsi_oversold and self.rsi[-2] > self.rsi_oversold:
                    # RSI crossed below oversold threshold - potential trend reversal
                    self.current_trade_entry_details["Exit Reason"] = "Trend Reversal Exit"
                    if self.debug_mode:
                        print(f"DEBUG - LONG TREND REVERSAL EXIT: RSI: {self.rsi[-1]}")

                    self.position.close()
                    self._record_exit_details(current_datetime, current_close)
                    self.current_trade_entry_details = {}
                    return
                # Check for volatility spike
                elif self.atr[-1] > self.atr[-5] * 1.5:
                    # ATR increased by 50% compared to 5 bars ago - volatility spike
                    self.current_trade_entry_details["Exit Reason"] = "Volatility Spike Exit"
                    if self.debug_mode:
                        print(f"DEBUG - LONG VOLATILITY SPIKE EXIT: ATR: {self.atr[-1]}, ATR(5 bars ago): {self.atr[-5]}")

                    self.position.close()
                    self._record_exit_details(current_datetime, current_close)
                    self.current_trade_entry_details = {}
                    return

            # For SHORT positions
            elif position_type == "SHORT":
                # Update lowest favorable price for trailing stop
                if np.isnan(self.highest_favorable_price) or current_low < self.highest_favorable_price:
                    self.highest_favorable_price = current_low  # For shorts, track lowest price

                # Debug logging
                if self.debug_mode:
                    print(f"DEBUG - SHORT Position Check: Time: {current_time}, Low: {current_low}, High: {current_high}, Close: {current_close}")
                    print(f"DEBUG - SHORT SL/TP Levels: SL: {sl_price}, TP: {tp_price}, Entry: {entry_price}")
                    print(f"DEBUG - SHORT Lowest Price: {self.highest_favorable_price}, Trailing Active: {self.trailing_stop_active}")

                # Check for trailing stop first (highest priority for profit protection)
                if self.trailing_stop_active and not np.isnan(self.trailing_stop_price):
                    if current_high >= self.trailing_stop_price:
                        self.current_trade_entry_details["Exit Reason"] = "Trailing Stop"
                        if self.debug_mode:
                            print(f"DEBUG - SHORT TRAILING STOP TRIGGERED: High: {current_high}, Trailing SL: {self.trailing_stop_price}")
                        self.position.close()
                        self._record_exit_details(current_datetime, self.trailing_stop_price)
                        self._reset_trade_variables()
                        return

                # Check if high price touched or exceeded stop loss
                elif current_high >= sl_price:
                    self.current_trade_entry_details["Exit Reason"] = "Stop Loss"
                    if self.debug_mode:
                        print(f"DEBUG - SHORT STOP LOSS TRIGGERED: High: {current_high}, SL: {sl_price}")
                    self.position.close()
                    self._record_exit_details(current_datetime, sl_price)
                    self._reset_trade_variables()
                    return

                # Check for partial take profit at 1:1 R:R
                elif self.partial_tp_enabled and not self.partial_tp_taken:
                    risk_points = sl_price - entry_price
                    partial_tp_level = entry_price - risk_points  # 1:1 R:R
                    if current_low <= partial_tp_level:
                        if self.debug_mode:
                            print(f"DEBUG - SHORT PARTIAL TP TRIGGERED: Low: {current_low}, Partial TP: {partial_tp_level}")
                        self.partial_tp_taken = True

                # Check if low price touched or went below take profit
                elif current_low <= tp_price:
                    self.current_trade_entry_details["Exit Reason"] = "Take Profit"
                    if self.debug_mode:
                        print(f"DEBUG - SHORT TAKE PROFIT TRIGGERED: Low: {current_low}, TP: {tp_price}")
                    self.position.close()
                    self._record_exit_details(current_datetime, tp_price)
                    self._reset_trade_variables()
                    return

                # Activate trailing stop when profit reaches trigger level
                elif self.trailing_stop_enabled and not self.trailing_stop_active:
                    risk_points = sl_price - entry_price
                    trailing_trigger_level = entry_price - (risk_points * self.trailing_stop_trigger)
                    if current_low <= trailing_trigger_level:
                        self.trailing_stop_active = True
                        self.trailing_stop_price = current_low + (self.atr[-1] * self.trailing_stop_distance)
                        if self.debug_mode:
                            print(f"DEBUG - SHORT TRAILING STOP ACTIVATED: Trigger: {trailing_trigger_level}, Initial Trail: {self.trailing_stop_price}")

                # Update trailing stop if active
                elif self.trailing_stop_active:
                    new_trailing_stop = self.highest_favorable_price + (self.atr[-1] * self.trailing_stop_distance)
                    if new_trailing_stop < self.trailing_stop_price:
                        self.trailing_stop_price = new_trailing_stop
                        if self.debug_mode:
                            print(f"DEBUG - SHORT TRAILING STOP UPDATED: New Trail: {self.trailing_stop_price}")
                # Check for trend reversal
                elif self.rsi[-1] > self.rsi_overbought and self.rsi[-2] < self.rsi_overbought:
                    # RSI crossed above overbought threshold - potential trend reversal
                    self.current_trade_entry_details["Exit Reason"] = "Trend Reversal Exit"
                    if self.debug_mode:
                        print(f"DEBUG - SHORT TREND REVERSAL EXIT: RSI: {self.rsi[-1]}")

                    self.position.close()
                    self._record_exit_details(current_datetime, current_close)
                    self.current_trade_entry_details = {}
                    return
                # Check for volatility spike
                elif self.atr[-1] > self.atr[-5] * 1.5:
                    # ATR increased by 50% compared to 5 bars ago - volatility spike
                    self.current_trade_entry_details["Exit Reason"] = "Volatility Spike Exit"
                    if self.debug_mode:
                        print(f"DEBUG - SHORT VOLATILITY SPIKE EXIT: ATR: {self.atr[-1]}, ATR(5 bars ago): {self.atr[-5]}")

                    self.position.close()
                    self._record_exit_details(current_datetime, current_close)
                    self.current_trade_entry_details = {}
                    return

        # --- Time-based Exit Logic ---
        # More robust time check using minutes conversion
        current_time_parts = current_time.split(':')
        current_time_minutes = int(current_time_parts[0]) * 60 + int(current_time_parts[1])

        exit_time_parts = self.exit_end_time.split(':')
        exit_time_minutes = int(exit_time_parts[0]) * 60 + int(exit_time_parts[1])

        # Close all positions if we're near market close (with 5 minute buffer)
        if self.position and current_time_minutes >= exit_time_minutes - 5:
            if self.debug_mode:
                print(f"DEBUG - TIME-BASED EXIT: Current time: {current_time}, Exit time: {self.exit_end_time}")

            # Store exit reason before closing position
            if self.current_trade_entry_details:
                self.current_trade_entry_details["Exit Reason"] = "Time-based Exit"
            self.position.close()

        # --- Check for Extended Breakout Window Expiry ---
        # Only exit due to window expiry if no trailing stop is active and we haven't taken partial profits
        if self.position and self.current_trade_entry_details and "Entry Bar Index" in self.current_trade_entry_details:
            # Calculate how many bars have passed since entry
            entry_bar_idx = self.current_trade_entry_details["Entry Bar Index"]
            bars_since_entry = current_idx - entry_bar_idx

            # Use extended window instead of the original short window
            extended_window = self.extended_breakout_window

            # Only exit due to window expiry if:
            # 1. Extended window has expired
            # 2. No trailing stop is active (meaning we haven't reached profit trigger)
            # 3. We haven't taken partial profits
            if (bars_since_entry >= extended_window and
                not self.trailing_stop_active and
                not self.partial_tp_taken):

                if self.debug_mode:
                    print(f"DEBUG - EXTENDED BREAKOUT WINDOW EXPIRED EXIT: Bars since entry: {bars_since_entry}, Extended Window: {extended_window}")
                    print(f"DEBUG - Trailing Stop Active: {self.trailing_stop_active}, Partial TP Taken: {self.partial_tp_taken}")

                # Store exit reason before closing position
                self.current_trade_entry_details["Exit Reason"] = "Breakout Window Expired"
                self.position.close()
                self._record_exit_details(current_datetime, current_close)
                self._reset_trade_variables()
                return

        # --- Check for Support/Resistance Level Change ---
        if self.position and self.current_trade_entry_details:
            position_type = self.current_trade_entry_details["Position"]

            # For LONG positions, check if support level has changed significantly
            if position_type == "LONG" and self.support_level[-1] != self.support_level[-2]:
                support_change_pct = abs(self.support_level[-1] - self.support_level[-2]) / self.support_level[-2] * 100

                # If support level changed by more than 0.5%, exit the position
                if support_change_pct > 0.5:
                    if self.debug_mode:
                        print(f"DEBUG - SUPPORT LEVEL CHANGE EXIT: Old: {self.support_level[-2]}, New: {self.support_level[-1]}")

                    # Store exit reason before closing position
                    self.current_trade_entry_details["Exit Reason"] = "Support/Resistance Level Change"
                    self.position.close()
                    self._record_exit_details(current_datetime, current_close)
                    self.current_trade_entry_details = {}
                    return

            # For SHORT positions, check if resistance level has changed significantly
            elif position_type == "SHORT" and self.resistance_level[-1] != self.resistance_level[-2]:
                resistance_change_pct = abs(self.resistance_level[-1] - self.resistance_level[-2]) / self.resistance_level[-2] * 100

                # If resistance level changed by more than 0.5%, exit the position
                if resistance_change_pct > 0.5:
                    if self.debug_mode:
                        print(f"DEBUG - RESISTANCE LEVEL CHANGE EXIT: Old: {self.resistance_level[-2]}, New: {self.resistance_level[-1]}")

                    # Store exit reason before closing position
                    self.current_trade_entry_details["Exit Reason"] = "Support/Resistance Level Change"
                    self.position.close()
                    self._record_exit_details(current_datetime, current_close)
                    self.current_trade_entry_details = {}
                    return

        # --- Trade Exit Logging (Before processing new entries) ---
        if self.current_trade_entry_details and not self.position:
            self._record_exit_details(current_datetime, current_close)
            self.current_trade_entry_details = {}

        if current_idx < 1:
            return

        # Skip entry logic if outside trading hours
        if current_time < self.entry_start_time or current_time >= self.exit_end_time:
            return

        previous_close = self.data.Close[-2]

        current_resistance_level = self.resistance[-2]
        current_support_level = self.support[-2]

        if np.isnan(current_resistance_level) or np.isnan(current_support_level):
            return

        res_band_upper = current_resistance_level * (1 + self.sr_band_percent)
        res_band_lower = current_resistance_level * (1 - self.sr_band_percent)
        sup_band_upper = current_support_level * (1 + self.sr_band_percent)
        sup_band_lower = current_support_level * (1 - self.sr_band_percent)

        if np.isnan(self.trend_ma[-1]) or np.isnan(self.trend_ma[-2]):
            return

        is_uptrend = current_close > self.trend_ma[-1] and self.trend_ma[-1] > self.trend_ma[-2]
        is_downtrend = current_close < self.trend_ma[-1] and self.trend_ma[-1] < self.trend_ma[-2]

        if self.breakout_candle_idx != -1 and current_idx - self.breakout_candle_idx > self.retest_window * 2:
            self._reset_breakout_state() # This calls _reset_retest_state internally

        # Upward breakout
        if previous_close <= current_resistance_level and current_close > current_resistance_level:
            if current_close - max(current_open, current_resistance_level) >= (current_high - current_low) * self.min_breakout_body_percent \
               and is_uptrend:
                self.breakout_up_level = current_resistance_level
                self.breakout_down_level = np.nan
                self.breakout_direction = 1
                self.breakout_candle_idx = current_idx
                self._reset_retest_state() # Ensure retest state is clean for new breakout

        # Downward breakout
        elif previous_close >= current_support_level and current_close < current_support_level:
            if min(current_open, current_support_level) - current_close >= (current_high - current_low) * self.min_breakout_body_percent \
               and is_downtrend:
                self.breakout_down_level = current_support_level
                self.breakout_up_level = np.nan
                self.breakout_direction = -1
                self.breakout_candle_idx = current_idx
                self._reset_retest_state() # Ensure retest state is clean for new breakout

        # --- Retest Confirmation & Entry Logic ---
        if not self.position:
            # Long Entry
            if self.breakout_direction == 1 and self.breakout_candle_idx != -1 and \
               current_idx > self.breakout_candle_idx and \
               current_idx <= self.breakout_candle_idx + self.retest_window:

                if not self.waiting_for_confirmation and \
                   (current_low <= self.breakout_up_level <= current_high or \
                    (current_low <= res_band_upper and current_high >= res_band_lower)):
                    self.retest_touch_candle_idx = current_idx
                    self.retest_touch_candle_high = current_high
                    self.retest_touch_candle_low = current_low
                    self.waiting_for_confirmation = True

                if self.waiting_for_confirmation and \
                   (current_idx == self.retest_touch_candle_idx + self.confirmation_lookback or current_idx == self.retest_touch_candle_idx + 1):
                    if current_close > self.retest_touch_candle_high:

                        if np.isnan(self.atr[-1]):
                            self._reset_retest_state()
                            return

                        sl = self.retest_touch_candle_low - (self.atr[-1] * self.sl_atr_multiplier)
                        sl = min(sl, self.retest_touch_candle_low)

                        if self.debug_mode:
                            print(f"DEBUG - LONG Entry Calculation:")
                            print(f"  Current Close: {current_close}")
                            print(f"  Retest Touch Low: {self.retest_touch_candle_low}")
                            print(f"  ATR: {self.atr[-1]}")
                            print(f"  SL ATR Multiplier: {self.sl_atr_multiplier}")
                            print(f"  Calculated SL: {sl}")

                        if sl >= current_close:
                             if self.debug_mode:
                                 print(f"DEBUG - LONG Entry Rejected: SL ({sl}) >= Current Close ({current_close})")
                             self._reset_retest_state()
                             return

                        # Calculate dynamic take profit based on volatility
                        tp_multiplier = self._calculate_dynamic_tp_multiplier()
                        tp = current_close + (current_close - sl) * tp_multiplier

                        if self.debug_mode:
                            print(f"DEBUG - LONG Entry Accepted:")
                            print(f"  Entry Price: {current_close}")
                            print(f"  Stop Loss: {sl}")
                            print(f"  Take Profit: {tp}")
                            print(f"  Risk (points): {current_close - sl}")
                            print(f"  Reward (points): {tp - current_close}")
                            print(f"  Dynamic R:R Ratio: {tp_multiplier}")
                            print(f"  ATR: {self.atr[-1]}, Vol Ratio: {self.atr[-1] / self.atr[-5] if self.atr[-5] > 0 else 1}")

                        self.buy(sl=sl, tp=tp, size=0.99)
                        self._record_entry_details(
                            entry_type="LONG",
                            entry_datetime=current_datetime,
                            entry_price=current_close,
                            sl_price=sl,
                            tp_price=tp
                        )
                        # Don't reset breakout state immediately - let the trade run
                        self._reset_retest_state()  # Only reset retest state

            # Short Entry
            elif self.breakout_direction == -1 and self.breakout_candle_idx != -1 and \
                 current_idx > self.breakout_candle_idx and \
                 current_idx <= self.breakout_candle_idx + self.retest_window:

                if not self.waiting_for_confirmation and \
                   (current_high >= self.breakout_down_level >= current_low or \
                    (current_high >= sup_band_lower and current_low <= sup_band_upper)):
                    self.retest_touch_candle_idx = current_idx
                    self.retest_touch_candle_high = current_high
                    self.retest_touch_candle_low = current_low
                    self.waiting_for_confirmation = True

                if self.waiting_for_confirmation and \
                   (current_idx == self.retest_touch_candle_idx + self.confirmation_lookback or current_idx == self.retest_touch_candle_idx + 1):
                    if current_close < self.retest_touch_candle_low:

                        if np.isnan(self.atr[-1]):
                            self._reset_retest_state()
                            return

                        sl = self.retest_touch_candle_high + (self.atr[-1] * self.sl_atr_multiplier)
                        sl = max(sl, self.retest_touch_candle_high)

                        if self.debug_mode:
                            print(f"DEBUG - SHORT Entry Calculation:")
                            print(f"  Current Close: {current_close}")
                            print(f"  Retest Touch High: {self.retest_touch_candle_high}")
                            print(f"  ATR: {self.atr[-1]}")
                            print(f"  SL ATR Multiplier: {self.sl_atr_multiplier}")
                            print(f"  Calculated SL: {sl}")

                        if sl <= current_close:
                            if self.debug_mode:
                                print(f"DEBUG - SHORT Entry Rejected: SL ({sl}) <= Current Close ({current_close})")
                            self._reset_retest_state()
                            return

                        # Calculate dynamic take profit based on volatility
                        tp_multiplier = self._calculate_dynamic_tp_multiplier()
                        tp = current_close - (sl - current_close) * tp_multiplier

                        if self.debug_mode:
                            print(f"DEBUG - SHORT Entry Accepted:")
                            print(f"  Entry Price: {current_close}")
                            print(f"  Stop Loss: {sl}")
                            print(f"  Take Profit: {tp}")
                            print(f"  Risk (points): {sl - current_close}")
                            print(f"  Reward (points): {current_close - tp}")
                            print(f"  Dynamic R:R Ratio: {tp_multiplier}")
                            print(f"  ATR: {self.atr[-1]}, Vol Ratio: {self.atr[-1] / self.atr[-5] if self.atr[-5] > 0 else 1}")

                        self.sell(sl=sl, tp=tp, size=0.99)
                        self._record_entry_details(
                            entry_type="SHORT",
                            entry_datetime=current_datetime,
                            entry_price=current_close,
                            sl_price=sl,
                            tp_price=tp
                        )
                        # Don't reset breakout state immediately - let the trade run
                        self._reset_retest_state()  # Only reset retest state
            else:
                # If waiting for confirmation but conditions aren't met within the lookback, reset
                if self.waiting_for_confirmation and \
                   current_idx > self.retest_touch_candle_idx + self.confirmation_lookback + 1:
                    self._reset_retest_state()

    def _record_entry_details(self, entry_type, entry_datetime, entry_price, sl_price, tp_price):
        """Records the entry details of a new trade."""
        # Get current bar index for tracking
        current_idx = len(self.data.Close) - 1

        # Use extended breakout window for better trade development
        remaining_window = self.extended_breakout_window

        self.current_trade_entry_details = {
            "Index": self.trade_index_counter,
            "Entry DateTime": entry_datetime,
            "Entry Price": entry_price,
            "Position": entry_type,
            "SL Price": sl_price,
            "TP Price": tp_price,
            "Breakout Window": remaining_window,  # Extended window for trade development
            "Breakout Candle Idx": self.breakout_candle_idx,
            "Entry Bar Index": current_idx  # Store entry bar index for tracking
        }
        self.trade_index_counter += 1

        # Reset enhanced TP tracking variables for new trade
        self.trailing_stop_price = np.nan
        self.trailing_stop_active = False
        self.partial_tp_taken = False
        self.highest_favorable_price = np.nan

    def _record_exit_details(self, exit_datetime, exit_price):
        """
        Records the exit details for the currently open trade.
        Uses predefined exit reasons or infers based on market conditions.
        """
        if not self.current_trade_entry_details:
            return

        entry_type = self.current_trade_entry_details["Position"]
        entry_price = self.current_trade_entry_details["Entry Price"]
        sl_price = self.current_trade_entry_details["SL Price"]
        tp_price = self.current_trade_entry_details["TP Price"]

        # Calculate profit points - this calculation is correct and should remain consistent
        profit_points = exit_price - entry_price if entry_type == "LONG" else entry_price - exit_price

        # Check if exit reason was already set (e.g., for time-based exits)
        if "Exit Reason" in self.current_trade_entry_details:
            exit_reason = self.current_trade_entry_details["Exit Reason"]

            # If the exit reason is "Stop Loss" but profit is positive, or
            # if the exit reason is "Take Profit" but profit is negative,
            # we need to correct the exit reason to be more accurate
            if entry_type == "LONG":
                if exit_reason == "Stop Loss" and exit_price > entry_price:
                    exit_reason = "Trend Reversal Exit"
                elif exit_reason == "Take Profit" and exit_price < entry_price:
                    exit_reason = "Volatility Spike Exit"
            elif entry_type == "SHORT":
                if exit_reason == "Stop Loss" and exit_price < entry_price:
                    exit_reason = "Trend Reversal Exit"
                elif exit_reason == "Take Profit" and exit_price > entry_price:
                    exit_reason = "Volatility Spike Exit"
        else:
            # Infer Exit Reason without using tolerance-based calculations
            exit_reason = "Unknown"

            # Get current market conditions
            exit_time = exit_datetime.strftime('%H:%M')
            exit_date = exit_datetime.strftime('%Y-%m-%d')
            entry_date = self.current_trade_entry_details["Entry DateTime"].strftime('%Y-%m-%d')

            # Check for specific exit conditions
            if entry_type == "LONG":
                # Check for overnight exit
                if exit_date != entry_date and exit_time == "09:15":
                    exit_reason = "Overnight Exit"
                # Check for time-based exit
                elif exit_time >= self.exit_end_time:
                    exit_reason = "Time-based Exit"
                # Check for price action based exits
                elif exit_price <= sl_price:
                    exit_reason = "Stop Loss"
                elif exit_price >= tp_price:
                    exit_reason = "Take Profit"
                # More granular exit reasons based on market behavior
                elif exit_price < entry_price:
                    # Price moved down but not to stop loss
                    if self.rsi[-1] < self.rsi_oversold:  # Oversold condition
                        exit_reason = "Support/Resistance Level Change"
                    elif self.atr[-1] > self.atr[-5] * 1.5:  # Volatility spike
                        exit_reason = "Volatility Spike Exit"
                    else:
                        exit_reason = "Trend Reversal Exit"
                elif exit_price > entry_price:
                    # Price moved up but not to take profit
                    if self.current_trade_entry_details.get("Breakout Window", 0) > 0:
                        exit_reason = "Breakout Window Expired"
                    else:
                        exit_reason = "Manual/Other Exit"

            elif entry_type == "SHORT":
                # Check for overnight exit
                if exit_date != entry_date and exit_time == "09:15":
                    exit_reason = "Overnight Exit"
                # Check for time-based exit
                elif exit_time >= self.exit_end_time:
                    exit_reason = "Time-based Exit"
                # Check for price action based exits
                elif exit_price >= sl_price:
                    exit_reason = "Stop Loss"
                elif exit_price <= tp_price:
                    exit_reason = "Take Profit"
                # More granular exit reasons based on market behavior
                elif exit_price > entry_price:
                    # Price moved up but not to stop loss
                    if self.rsi[-1] > self.rsi_overbought:  # Overbought condition
                        exit_reason = "Support/Resistance Level Change"
                    elif self.atr[-1] > self.atr[-5] * 1.5:  # Volatility spike
                        exit_reason = "Volatility Spike Exit"
                    else:
                        exit_reason = "Trend Reversal Exit"
                elif exit_price < entry_price:
                    # Price moved down but not to take profit
                    if self.current_trade_entry_details.get("Breakout Window", 0) > 0:
                        exit_reason = "Breakout Window Expired"
                    else:
                        exit_reason = "Manual/Other Exit"

        duration = exit_datetime - self.current_trade_entry_details["Entry DateTime"]

        # Ensure entry_price is not zero to avoid division by zero
        profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0

        # Validate exit reason and profit points consistency
        # For Stop Loss, profit should be negative (or zero in rare cases)
        # For Take Profit, profit should be positive (or zero in rare cases)
        if exit_reason == "Stop Loss" and profit_points > 0:
            # If we have a positive profit but Stop Loss exit reason, change to Trend Reversal
            exit_reason = "Trend Reversal Exit"
        elif exit_reason == "Take Profit" and profit_points < 0:
            # If we have a negative profit but Take Profit exit reason, change to Volatility Spike
            exit_reason = "Volatility Spike Exit"

        trade_entry = {
            "Index": self.current_trade_entry_details["Index"],
            "Entry DateTime": self.current_trade_entry_details["Entry DateTime"].strftime('%Y-%m-%d %H:%M:%S'),
            "Exit DateTime": exit_datetime.strftime('%Y-%m-%d %H:%M:%S'),
            "Entry Price": round(entry_price, 2),
            "Exit Price": round(exit_price, 2),
            "Exit Reason": exit_reason,
            "Profit Points": round(profit_points, 2),
            "Profit Percent": round(profit_percent, 2),
            "Position": entry_type,
            "Trade Duration": str(duration)
        }

        self.trade_log.append(trade_entry)

        if self.debug_mode:
            print(f"DEBUG - Trade logged: {trade_entry}")
            print(f"DEBUG - Total trades in log: {len(self.trade_log)}")

    def _calculate_dynamic_tp_multiplier(self):
        """Calculate dynamic take profit multiplier based on market volatility."""
        if not self.dynamic_tp_enabled:
            return self.tp_rr

        # Calculate volatility ratio (current ATR vs recent average)
        if len(self.atr) < 5 or self.atr[-5] == 0:
            return self.tp_rr

        vol_ratio = self.atr[-1] / self.atr[-5]

        if vol_ratio > self.vol_threshold:
            # High volatility - reduce TP to capture profits quicker
            return self.high_vol_tp_multiplier
        else:
            # Low volatility - increase TP for better R:R
            return self.low_vol_tp_multiplier

    def _reset_trade_variables(self):
        """Reset all trade-related variables when a trade is closed."""
        self.current_trade_entry_details = {}
        self.trailing_stop_price = np.nan
        self.trailing_stop_active = False
        self.partial_tp_taken = False
        self.highest_favorable_price = np.nan
        self._reset_breakout_state()

    def _reset_breakout_state(self):
        """Resets all breakout-related state variables."""
        self.breakout_up_level = np.nan
        self.breakout_down_level = np.nan
        self.breakout_direction = 0
        self.breakout_candle_idx = -1
        self._reset_retest_state() # Calls the retest state reset

    def _reset_retest_state(self):
        """Resets all retest-related state variables."""
        self.retest_touch_candle_idx = -1
        self.retest_touch_candle_high = np.nan
        self.retest_touch_candle_low = np.nan
        self.waiting_for_confirmation = False

    def close_all_trades(self):
        """
        This method will be called by Backtest after the backtest is finished,
        to ensure any open positions are closed and logged.
        """
        if self.position and self.current_trade_entry_details:
            last_close = self.data.Close[-1]
            last_datetime = self.data.index[-1]
            self._record_exit_details(last_datetime, last_close)
            self._reset_trade_variables()