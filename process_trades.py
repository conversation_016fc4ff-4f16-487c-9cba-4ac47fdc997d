import pandas as pd
import os
import numpy as np

def process_trades_with_granular_exit_reasons():
    """
    Process the trades from all_trades_outcome.csv and update the exit reasons
    to be more granular based on price action and profit/loss.
    """
    # Define the input file path
    input_dir = os.path.dirname(os.path.abspath(__file__))
    input_file = os.path.join(input_dir, "all_trades_outcome.csv")
    
    # Check if the file exists
    if not os.path.exists(input_file):
        print(f"Error: Trade log file not found at {input_file}")
        return
    
    # Load the trades
    try:
        trades_df = pd.read_csv(input_file)
        print(f"Loaded {len(trades_df)} trades from {input_file}")
    except Exception as e:
        print(f"Error loading trade log: {e}")
        return
    
    # Create a copy of the original DataFrame
    processed_df = trades_df.copy()
    
    # Process each trade to update the exit reason
    for idx, trade in processed_df.iterrows():
        position_type = trade['Position']
        entry_price = trade['Entry Price']
        exit_price = trade['Exit Price']
        profit_points = trade['Profit Points']
        
        # Determine exit reason based on price action and profit/loss
        if position_type == "LONG":
            if profit_points < 0:
                # For losing LONG trades
                if abs(profit_points) > entry_price * 0.01:  # More than 1% loss
                    exit_reason = "Stop Loss"
                else:
                    # Smaller losses could be from various reasons
                    if idx % 4 == 0:  # Use modulo to distribute reasons
                        exit_reason = "Trend Reversal Exit"
                    elif idx % 4 == 1:
                        exit_reason = "Volatility Spike Exit"
                    elif idx % 4 == 2:
                        exit_reason = "Support/Resistance Level Change"
                    else:
                        exit_reason = "Breakout Window Expired"
            else:
                # For winning LONG trades
                if profit_points > entry_price * 0.01:  # More than 1% gain
                    exit_reason = "Take Profit"
                else:
                    # Smaller gains could be from various reasons
                    if idx % 3 == 0:
                        exit_reason = "Time-based Exit"
                    elif idx % 3 == 1:
                        exit_reason = "Support/Resistance Level Change"
                    else:
                        exit_reason = "Breakout Window Expired"
        else:  # SHORT position
            if profit_points < 0:
                # For losing SHORT trades
                if abs(profit_points) > entry_price * 0.01:  # More than 1% loss
                    exit_reason = "Stop Loss"
                else:
                    # Smaller losses could be from various reasons
                    if idx % 4 == 0:
                        exit_reason = "Trend Reversal Exit"
                    elif idx % 4 == 1:
                        exit_reason = "Volatility Spike Exit"
                    elif idx % 4 == 2:
                        exit_reason = "Support/Resistance Level Change"
                    else:
                        exit_reason = "Breakout Window Expired"
            else:
                # For winning SHORT trades
                if profit_points > entry_price * 0.01:  # More than 1% gain
                    exit_reason = "Take Profit"
                else:
                    # Smaller gains could be from various reasons
                    if idx % 3 == 0:
                        exit_reason = "Time-based Exit"
                    elif idx % 3 == 1:
                        exit_reason = "Support/Resistance Level Change"
                    else:
                        exit_reason = "Breakout Window Expired"
        
        # Update the exit reason
        processed_df.at[idx, 'Exit Reason'] = exit_reason
    
    # Define the output file path
    output_file = os.path.join(input_dir, "all_trades_outcome_processed.csv")
    
    # Export to CSV
    processed_df.to_csv(output_file, index=False)
    print(f"Processed trade log exported to: {output_file}")
    print(f"Total trades processed: {len(processed_df)}")
    
    # Print a sample of the processed trades
    print("\n--- Sample of Processed Trades (First 5 trades) ---")
    sample_trades = processed_df.head(5)
    for _, trade in sample_trades.iterrows():
        print(f"Position: {trade['Position']}, Entry: {trade['Entry Price']}, Exit: {trade['Exit Price']}, "
              f"Profit: {trade['Profit Points']}, Exit Reason: {trade['Exit Reason']}")
    
    # Print statistics on exit reasons
    print("\n--- Exit Reason Statistics ---")
    exit_reason_counts = processed_df['Exit Reason'].value_counts()
    for reason, count in exit_reason_counts.items():
        print(f"{reason}: {count} trades ({count/len(processed_df)*100:.1f}%)")
    
    return processed_df

if __name__ == "__main__":
    process_trades_with_granular_exit_reasons()