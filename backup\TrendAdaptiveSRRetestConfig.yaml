# NIFTY Strategy Backtest Configuration

# Data Configuration
data:
  file_path: "C:\\Users\\<USER>\\Desktop\\Python\\data\\NIFTY_1min.csv"
  date_column: "date"
  parse_dates: true
  index_column: "date"
  
  # Data filtering for testing (set to null for full data)
  start_date: "2015-01-01"
  end_date: "2025-04-17"
  
  # Expected column names for backtesting
  required_columns: ["Open", "High", "Low", "Close", "Volume"]

# Backtest Configuration
backtest:
  cash: 30000
  margin: 0.1  # 1/10
  commission: 0.0
  trade_on_close: true
  hedging: false

# Strategy Parameters
strategy:
  # Support/Resistance Detection
  n_periods: 50
  sr_band_percent: 0.001
  
  # Breakout Configuration
  min_breakout_body_percent: 0.5
  retest_window: 3
  confirmation_lookback: 1
  
  # Risk Management
  sl_atr_multiplier: 1.5
  tp_rr: 2  # Take profit risk-reward ratio
  
  # Technical Indicators
  atr_period: 14
  trend_ma_period: 100
  
  # Trading Hours
  market_start_time: "09:15"
  market_end_time: "15:30"
  entry_start_time: "09:15"
  exit_end_time: "15:15"
  
  # Trade Execution
  position_size: 0.99

# Output Configuration
output:
  trade_log_file: "all_trades_outcome.csv"
  required_trade_columns:
    - "Index"
    - "Entry DateTime"
    - "Exit DateTime"
    - "Entry Price"
    - "Exit Price"
    - "Profit Points"
    - "Profit Percent"
    - "Position"
    - "Trade Duration"
    - "Exit Reason"

# Logging Configuration
logging:
  enable_debug: false
  enable_strategy_state_debug: true
  enable_detailed_metrics: true
