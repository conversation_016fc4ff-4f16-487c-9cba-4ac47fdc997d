import pandas as pd
import numpy as np
from backtesting import Backtest, Strategy
from backtesting.lib import crossover, resample_apply
from talib import RSI, ATR # <--- CORRECTED IMPORT for RSI and ATR
from backtesting.test import SMA # A simple moving average indicator from backtesting.test
import os

# --- Calmar Ratio Calculation Function (for independent verification/reporting) ---
# --- Calmar Ratio Calculation Function (for independent verification/reporting) ---
def calculate_calmar_ratio(returns_series, annual_periods=252):
    """
    Calculates the Calmar Ratio for a given series of daily returns.
    This is useful for custom reporting beyond what backtesting.py provides directly.
    """
    if not isinstance(returns_series, pd.Series):
        returns_series = pd.Series(returns_series)

    # Ensure there are enough periods for a meaningful calculation
    if len(returns_series) < 2: # Need at least two points to calculate return/drawdown
        return np.nan

    cumulative_returns = (1 + returns_series).cumprod()

    # Ensure cumulative_returns starts at 1.0 for correct drawdown calculation if it doesn't already
    if cumulative_returns.iloc[0] != 1.0: # Check if the series genuinely starts from 1.0
        # If it's a series of daily returns that don't represent the *start* of capital,
        # then cumulative_returns already factors in the first day's gain/loss.
        # However, for drawdown, we typically compare against the *initial* capital.
        # backtesting.py's '_equity_returns' is already the daily percentage change from the previous day's equity.
        # So (1+returns_series).cumprod() should naturally build the curve from 1.0 if conceptual.
        # If the index is not numeric, pd.Timedelta(days=1) might be better than minutes=1 for daily returns.
        pass # Let's assume backtesting.py's '_equity_returns' is already suitable.

    running_max = cumulative_returns.cummax()
    drawdowns = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdowns.min()

    if max_drawdown == 0:
        return np.inf if cumulative_returns.iloc[-1] > 1 else np.nan # Infinite if profit and no drawdown, NaN if no profit
        # If final equity is higher than initial and no drawdown, Calmar is technically infinite.
        # But for practical purposes, if max_drawdown is exactly zero, it often means very little activity
        # or consistently rising equity. Handle it by returning NaN or Inf.

    total_return = cumulative_returns.iloc[-1] - 1
    num_periods = len(returns_series)

    # Annualize based on the number of actual returns (e.g., daily returns)
    # The current code correctly uses compound annual growth rate (CAGR) formula.
    # If using daily returns, annual_periods should be 252 for equity, but your dummy data might not be 252 days.
    # For short durations, annualization can be highly misleading.
    # Consider adjusting annual_periods or acknowledging its impact on short runs.
    annualized_return = (1 + total_return)**(annual_periods / num_periods) - 1

    calmar_ratio = annualized_return / abs(max_drawdown)
    return calmar_ratio

# --- Base Strategy Class with Time Filtering ---
class TimeFilteredStrategy(Strategy):
    """
    Base strategy class to incorporate market hours and entry/exit time windows.
    Specific trading logic will be implemented in subclasses.
    """
    market_start_time = "09:15"
    market_end_time = "15:30"
    entry_start_time = "09:30"
    entry_end_time = "15:00" # Stop entering trades 30 mins before market close
    exit_start_time = "09:15" # Can exit any time during market hours
    exit_end_time = "15:29" # Ensure all positions are closed before market end

    def init(self):
        # Call init of superclass (if any)
        pass

    def next(self):
        current_time = self.data.index[-1].time()

        # Check if current time is within market hours (data filtering should mostly handle this)
        if not (pd.to_datetime(self.market_start_time).time() <= current_time <= pd.to_datetime(self.market_end_time).time()):
            # If outside market hours, ensure no positions are open
            if self.position:
                self.position.close()
            return # Do nothing outside market hours

        # Logic for exiting trades (e.g., forced close before market end)
        if self.position:
            if not (pd.to_datetime(self.exit_start_time).time() <= current_time <= pd.to_datetime(self.exit_end_time).time()):
                self.position.close() # Close position if outside exit window
                return

        # Check if current time is within entry window for opening new trades
        self.can_enter_trade = (pd.to_datetime(self.entry_start_time).time() <= current_time <= pd.to_datetime(self.entry_end_time).time())

        # Subclasses will use self.can_enter_trade to decide whether to open a new position


# --- Strategy 1: RSI Mean Reversion with Strict Stop-Loss & Take-Profit ---
class RsiMeanReversionStrategy(TimeFilteredStrategy): # Inherit from TimeFilteredStrategy
    """
    A mean-reversion strategy based on RSI (Relative Strength Index).
    Aims for good Calmar Ratio by:
    - Entering only on oversold/overbought conditions (potential mean reversion).
    - Implementing strict stop-loss to limit downside.
    - Implementing take-profit to lock in consistent small gains.
    """
    rsi_period = 14
    rsi_oversold = 30
    rsi_overbought = 70
    sl_multiplier = 2.0  # Stop-loss as multiple of ATR
    tp_multiplier = 3.0  # Take-profit as multiple of ATR

    def init(self):
        super().init() # Call base class init
        self.rsi = self.I(RSI, self.data.Close, self.rsi_period)
        self.atr = self.I(ATR, self.data.High, self.data.Low, self.data.Close, self.rsi_period)

    def next(self):
        super().next() # Call base class next for time filtering and position management

        if not self.can_enter_trade and not self.position: # If no position and cannot enter, just return
            return
        if self.position and not self.can_enter_trade: # Allow existing position to be managed by time exit if needed
            return

        atr_value = self.atr[-1]
        sl_distance = atr_value * self.sl_multiplier
        tp_distance = atr_value * self.tp_multiplier

        if sl_distance <= 0 or tp_distance <= 0: # Avoid division by zero or invalid distances
            return

        # Long Entry (Buy)
        if self.can_enter_trade and not self.position:
            if self.rsi[-1] < self.rsi_oversold:
                # Add a simple confirmation, e.g., price is currently rising
                if self.data.Close[-1] > self.data.Close[-2]:
                    self.buy(
                        sl=self.data.Close[-1] - sl_distance,
                        tp=self.data.Close[-1] + tp_distance
                    )

        # Short Entry (Sell)
        elif self.can_enter_trade and not self.position:
            if self.rsi[-1] > self.rsi_overbought:
                # Add a simple confirmation, e.g., price is currently falling
                if self.data.Close[-1] < self.data.Close[-2]:
                    self.sell(
                        sl=self.data.Close[-1] + sl_distance,
                        tp=self.data.Close[-1] - tp_distance
                    )


# --- Strategy 2: Dual Moving Average Crossover with Adaptive Risk ---
class DualMAAdaptiveStrategy(TimeFilteredStrategy): # Inherit from TimeFilteredStrategy
    """
    A dual moving average crossover strategy.
    Aims for good Calmar Ratio by:
    - Capturing trends (MA crossovers).
    - Using fixed risk per trade (cash_at_risk).
    - Adapting trade size based on volatility (ATR).
    - Implementing stop-loss based on recent volatility.
    """
    fast_ma_period = 10
    slow_ma_period = 30
    sl_atr_multiplier = 2.5 # Stop-loss as multiple of ATR
    max_risk_per_trade_percent = 0.01 # 1% of equity

    def init(self):
        super().init() # Call base class init
        self.fast_ma = self.I(SMA, self.data.Close, self.fast_ma_period)
        self.slow_ma = self.I(SMA, self.data.Close, self.slow_ma_period)
        self.atr = self.I(ATR, self.data.High, self.data.Low, self.data.Close, self.slow_ma_period)

    def next(self):
        super().next() # Call base class next for time filtering and position management

        if not self.can_enter_trade and not self.position: # If no position and cannot enter, just return
            return
        if self.position and not self.can_enter_trade: # Allow existing position to be managed by time exit if needed
            return

        current_atr = self.atr[-1]
        sl_distance = current_atr * self.sl_atr_multiplier

        if sl_distance <= 0: # Avoid invalid stop-loss distance
            return

        cash_to_risk = self.equity * self.max_risk_per_trade_percent

        # Long Crossover (Buy Signal)
        if self.can_enter_trade and not self.position:
            if crossover(self.fast_ma, self.slow_ma):
                shares_to_buy = int(cash_to_risk / sl_distance)
                if shares_to_buy > 0:
                    self.buy(
                        size=shares_to_buy,
                        sl=self.data.Close[-1] - sl_distance
                    )

        # Short Crossover (Sell Signal)
        elif self.can_enter_trade and not self.position:
            if crossover(self.slow_ma, self.fast_ma):
                shares_to_sell = int(cash_to_risk / sl_distance)
                if shares_to_sell > 0:
                    self.sell(
                        size=shares_to_sell,
                        sl=self.data.Close[-1] + sl_distance
                    )


# --- Strategy 3: Volatility Breakout with Time Exit ---
class VolatilityBreakoutStrategy(TimeFilteredStrategy): # Inherit from TimeFilteredStrategy
    """
    A strategy that buys breakouts from periods of low volatility.
    Aims for good Calmar Ratio by:
    - Catching potentially strong moves.
    - Exiting based on time to avoid being stuck in range-bound markets or reversals.
    - Strict stop-loss.
    """
    atr_period = 20
    breakout_threshold_atr = 1.0 # Price move must exceed this multiple of ATR
    exit_bars_after_entry = 10 # Exit trade after this many bars if not stopped out/taken profit

    def init(self):
        super().init() # Call base class init
        self.atr = self.I(ATR, self.data.High, self.data.Low, self.data.Close, self.atr_period)
        self.entry_bar_idx = {}

    def next(self):
        super().next() # Call base class next for time filtering and position management

        current_atr = self.atr[-1]
        sl_distance = current_atr * 2.0 # Fixed stop-loss multiple of ATR

        if sl_distance <= 0:
            return

        # Long Entry
        if self.can_enter_trade and not self.position:
            # Simple breakout: current close significantly higher than previous close
            if (self.data.Close[-1] - self.data.Close[-2]) > (current_atr * 0.5) and \
               (self.data.High[-1] - self.data.Low[-1]) > (current_atr * self.breakout_threshold_atr):
                self.buy(
                    sl=self.data.Close[-1] - sl_distance
                )
                self.entry_bar_idx[self.position.uid] = self.data.index.get_loc(self.data.index[-1])

        # Manage existing positions
        if self.position:
            # Time-based exit for open positions
            if self.data.index.get_loc(self.data.index[-1]) - self.entry_bar_idx.get(self.position.uid, -self.exit_bars_after_entry) >= self.exit_bars_after_entry:
                self.position.close()
                if self.position.uid in self.entry_bar_idx:
                    del self.entry_bar_idx[self.position.uid] # Clean up


# --- Main Backtesting Execution ---
if __name__ == '__main__':
    # --- Configuration ---
    CSV_FILE_PATH = 'your_ohlc_data.csv' # <--- IMPORTANT: Change this to your CSV file path!
    # Make sure your CSV has columns like 'Date', 'Open', 'High', 'Low', 'Close'
    # 'Date' column should be parsable into datetime objects (e.g., 'YYYY-MM-DD HH:MM:SS')

    # Define market hours (e.g., for Indian stock market)
    _market_start_time = "09:15"
    _market_end_time = "15:30"

    # Define strategy entry/exit windows within market hours
    _entry_start_time = "09:30"  # Don't trade immediately at open
    _entry_end_time = "15:00"    # Stop entering new trades half an hour before close
    _exit_start_time = "09:15"   # Can exit any time after market open
    _exit_end_time = "15:29"     # Close all positions 1 minute before market close

    # --- Load and Prepare Data ---
    if not os.path.exists(CSV_FILE_PATH):
        print(f"Error: CSV file not found at '{CSV_FILE_PATH}'. Please update the path.")
        # Create a dummy CSV for demonstration if file not found
        print("Creating a dummy CSV for demonstration purposes...")
        dummy_data_points = 5000 # More points for intraday simulation
        dummy_dates = pd.date_range(start='2023-01-01 09:15:00', periods=dummy_data_points, freq='min')
        dummy_data = pd.DataFrame(index=dummy_dates)
        dummy_data['Open'] = 100 + np.cumsum(np.random.randn(dummy_data_points) * 0.1)
        dummy_data['High'] = dummy_data['Open'] + np.abs(np.random.randn(dummy_data_points) * 0.5)
        dummy_data['Low'] = dummy_data['Open'] - np.abs(np.random.randn(dummy_data_points) * 0.5)
        dummy_data['Close'] = dummy_data['Open'] + np.random.randn(dummy_data_points) * 0.1
        dummy_data['Volume'] = np.random.randint(100, 1000, dummy_data_points)
        dummy_data.index.name = 'Date'
        dummy_data.to_csv(CSV_FILE_PATH)
        print(f"Dummy CSV created at '{CSV_FILE_PATH}'. Please replace it with your actual data.")


    try:
        data = pd.read_csv(CSV_FILE_PATH, index_col='Date', parse_dates=True)
        # Ensure column names are standardized for backtesting.py
        data.columns = [col.capitalize() for col in data.columns]

        # Add 'Volume' column if it's missing (pre-fill with zeros)
        if 'Volume' not in data.columns:
            data['Volume'] = 0
            print("Warning: 'Volume' column not found. Added with zeros.")

        # Filter data based on market start and end times
        data['Time'] = data.index.time
        data = data[(data['Time'] >= pd.to_datetime(_market_start_time).time()) &
                    (data['Time'] <= pd.to_datetime(_market_end_time).time())]
        data = data.drop(columns=['Time']) # Remove the temporary 'Time' column

        # Ensure correct column order for backtesting.py (Open, High, Low, Close, Volume)
        data = data[['Open', 'High', 'Low', 'Close', 'Volume']]

    except Exception as e:
        print(f"Error loading or processing CSV: {e}")
        exit() # Exit if data loading fails

    # --- Set Strategy Parameters (Pass time configs to strategies) ---
    strategies_to_test = [
        (RsiMeanReversionStrategy, "RSI Mean Reversion"),
        (DualMAAdaptiveStrategy, "Dual MA Adaptive"),
        (VolatilityBreakoutStrategy, "Volatility Breakout")
    ]

    for StrategyClass, strategy_name in strategies_to_test:
        # Set market and entry/exit times for each strategy instance
        StrategyClass.market_start_time = _market_start_time
        StrategyClass.market_end_time = _market_end_time
        StrategyClass.entry_start_time = _entry_start_time
        StrategyClass.entry_end_time = _entry_end_time
        StrategyClass.exit_start_time = _exit_start_time
        StrategyClass.exit_end_time = _exit_end_time

        print(f"\n--- Backtesting {strategy_name} ---")
        bt = Backtest(data, StrategyClass,
                      cash=100000, commission=0.002, exclusive_orders=True) # 0.2% commission
        stats = bt.run()
        print(stats)

        if 'Returns' in stats and len(stats['_equity_returns']) > 0:
            daily_returns = stats['_equity_returns']
            calmar = calculate_calmar_ratio(daily_returns)
            print(f"Calculated Calmar Ratio ({strategy_name}): {calmar:.4f}")
        else:
            print(f"Could not calculate Calmar Ratio for {strategy_name} (no returns data).")

        # Uncomment to plot results for each strategy
        # bt.plot()

    print("\nBacktesting complete.")