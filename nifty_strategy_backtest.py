import pandas as pd
import numpy as np
import os
from backtesting import Backtest
from strategies import SupportResistanceBreakoutRetestImprovedCSV

# --- Main Execution ---
if __name__ == '__main__':
    # Load the data
    file_path = 'C:\\Users\\<USER>\\Desktop\\Python\\data\\NIFTY_1min.csv'
    try:
        data = pd.read_csv(
            file_path,
            parse_dates=['date'],
            index_col='date'
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming)
    # backtesting.py expects: 'Open', 'High', 'Low', 'Close', 'Volume'
    column_map_lower = {col.lower(): col for col in data.columns}

    rename_dict = {}
    # Map standard OHLC names first
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        else:
            # Check if the correctly cased 'bt_name' (e.g. 'Open') already exists
            if bt_name not in data.columns:
                 print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
                 exit()

    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present
    if 'volume' in column_map_lower:
        # If 'volume' (lowercase) exists, rename its original cased version to 'Volume'
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns: # Check if 'Volume' (exact case) is already present
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0

    # Verify final column names
    expected_bt_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns for backtesting.py are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")


    # Filter data for a smaller period for faster initial testing if needed
    # data = data['2022-01-01':'2022-03-31'] # Example: 3 months of data
    # print(f"Data filtered for testing. New shape: {data.shape}")

    # Filter data for testing - use just 1 week of data for faster testing    
    ###########################################################################################
    data = data.loc['2024-10-01':'2025-04-17'] 
    print(f"Data filtered for testing. New shape: {data.shape}")
    ###########################################################################################

    # Check for gaps in the data
    print("Checking for gaps in data...")
    data_dates = data.index.strftime('%Y-%m-%d').unique()
    gap_count = 0
    for i in range(len(data_dates) - 1):
        from_date = pd.to_datetime(data_dates[i])
        to_date = pd.to_datetime(data_dates[i+1])
        day_diff = (to_date - from_date).days
        if day_diff > 1:
            gap_count += 1
            if gap_count <= 5:  # Only show first 5 gaps to avoid flooding output
                print(f"Warning: Gap in data between {from_date.date()} and {to_date.date()} ({day_diff} days)")

    if gap_count > 5:
        print(f"... and {gap_count - 5} more gaps")

    print(f"Total gaps found: {gap_count}")

    # Initialize and run the backtest
    # Adjusted cash and margin: NIFTY futures lot size can be e.g. 50, price e.g. 20000
    # Margin for NIFTY futures can be around 10-15%, so 1/10 or 1/7.
    # Cash should be enough for a few lots. e.g., 50 * 20000 * 0.15 margin * 2 lots = 3,00,000

    # Create strategy instance with debug mode enabled
    strategy_class = SupportResistanceBreakoutRetestImprovedCSV
    strategy_class.debug_mode = False  # Enable debug mode for testing overnight fixes

    bt = Backtest(
        data,
        strategy_class,
        cash=30000,
        margin=1/10,
        commission=0.0,
        trade_on_close=True,  # Ensure trades are executed on the close of the candle
        hedging=False         # Disable hedging to ensure positions are closed properly
    )

    print("Running backtest...")
    # Suppress UserWarnings from backtesting.py about margin if they are too noisy,
    # but it's good to see them initially.
    # import warnings
    # with warnings.catch_warnings():
    #     warnings.filterwarnings('ignore', category=UserWarning, module='backtesting.backtesting')
    stats = bt.run()
    print("\n--- Backtest Performance Report ---")
    print(stats)

    # Get the strategy instance
    strategy_instance = bt._strategy

    # Print debug info about the strategy's state
    print("\n--- Strategy State Debug ---")
    print(f"Has trade_log attribute: {hasattr(strategy_instance, 'trade_log')}")
    if hasattr(strategy_instance, 'trade_log'):
        print(f"Trade log length: {len(strategy_instance.trade_log)}")

    print(f"Has current_trade_entry_details attribute: {hasattr(strategy_instance, 'current_trade_entry_details')}")
    if hasattr(strategy_instance, 'current_trade_entry_details'):
        print(f"Current trade entry details: {strategy_instance.current_trade_entry_details}")

    # Manually close any open trades to ensure the trade log is complete
    if hasattr(strategy_instance, 'close_all_trades'):
        try:
            # Call the method without 'self' parameter
            strategy_instance.close_all_trades.__func__(strategy_instance)
            print("Successfully closed all trades")
        except Exception as e:
            print(f"Error closing trades: {e}")

    # Detailed Metrics Calculation (some might be redundant with stats output but good for clarity)
    print(f"\n--- Detailed Metrics ---")
    print(f"Total Return (%):                 {stats['Return [%]']:.2f}%")
    if 'Return (Ann.) [%]' in stats: # May not be present if data < 1 year
        print(f"Annualized Return (%):            {stats['Return (Ann.) [%]']:.2f}%")
    else:
        print(f"Annualized Return (%):            N/A (duration < 1 year or not calculated)")
    print(f"Max Drawdown (%):                 {stats['Max. Drawdown [%]']:.2f}%")
    print(f"Sharpe Ratio (annualized):        {stats['Sharpe Ratio']:.2f} (Risk-free rate assumed 0%)")
    print(f"Sortino Ratio (annualized):       {stats['Sortino Ratio']:.2f}")
    print(f"Win Rate (%):                     {stats['Win Rate [%]']:.2f}%")
    print(f"Profit Factor:                    {stats['Profit Factor']:.2f}")
    print(f"Total Number of Trades:           {stats['# Trades']}")

    # Debug: Print the first few trades to understand the structure
    all_trades = stats['_trades']
    if not all_trades.empty and len(all_trades) > 0:
        print("\n--- First Trade Details (Debug) ---")
        first_trade = all_trades.iloc[0]
        for col in first_trade.index:
            print(f"{col}: {first_trade[col]}")

    all_trades = stats['_trades']
    if not all_trades.empty:
        avg_trade_pnl_points = all_trades['PnL'].mean() # PnL is in cash, need to convert if points needed

        # For NIFTY, PnL is already in points if 1 unit is traded.
        # If PnL is cash and you need points, you'd divide by lot size * point value.
        # Assuming PnL from backtesting.py with 1 unit trade is effectively in points for NIFTY.
        print(f"Average Trade PnL (points):       {all_trades['PnL'].mean():.2f}")

        winning_trades = all_trades[all_trades['PnL'] > 0]
        losing_trades = all_trades[all_trades['PnL'] < 0]

        avg_winning_trade_points = winning_trades['PnL'].mean() if not winning_trades.empty else 0
        avg_losing_trade_points = losing_trades['PnL'].mean() if not losing_trades.empty else 0

        print(f"Average Winning Trade (points):   {avg_winning_trade_points:.2f}")
        print(f"Average Losing Trade (points):    {avg_losing_trade_points:.2f}")

        # Average Trade Duration (in bars)
        avg_trade_duration_bars = all_trades['Duration'].mean()
        print(f"Average Trade Duration (bars):    {avg_trade_duration_bars}")
        # To convert to time, you'd need the frequency of your data (e.g., 1 bar = 1 minute)
        # avg_trade_duration_time = pd.to_timedelta(avg_trade_duration_bars * pd.Timedelta(minutes=1)) # Example for 1-min data
        # print(f"Average Trade Duration (time):    {avg_trade_duration_time}")

    else:
        print("No trades were executed.")

    # Export trade log to CSV
    print("\n--- Exporting Trade Log to CSV ---")

    # Get the strategy instance to access the trade_log
    # bt._strategy returns the strategy class, we need the actual instance
    # The strategy instance is stored in bt._results
    try:
        # Try to get the strategy instance from the results
        strategy_instance = bt._results._strategy
        print(f"Strategy instance from _results: {type(strategy_instance)}")
    except:
        # Fallback to the original method
        strategy_instance = bt._strategy
        print(f"Strategy instance from _strategy: {type(strategy_instance)}")

    # Debug: Print strategy instance info
    #print(f"Strategy instance type: {type(strategy_instance)}")
    #print(f"Strategy instance attributes: {[attr for attr in dir(strategy_instance) if not attr.startswith('_')]}")

    # We don't need to manually call close_all_trades as it's a method that should be
    # called by the backtesting framework, not directly

    # First, try to use the strategy's internal trade log if it exists and has entries
    strategy_has_trade_log = hasattr(strategy_instance, 'trade_log') and strategy_instance.trade_log

    # Additional debug info
    if hasattr(strategy_instance, 'trade_log'):
        print(f"trade_log exists, length: {len(strategy_instance.trade_log)}")
        print(f"trade_log type: {type(strategy_instance.trade_log)}")
    else:
        print("trade_log attribute does not exist")

    # Print debug info about the trade log
    if strategy_has_trade_log:
        print(f"Strategy has internal trade log with {len(strategy_instance.trade_log)} entries")
        if len(strategy_instance.trade_log) > 0:
            print("First entry in trade log:")
            for key, value in strategy_instance.trade_log[0].items():
                print(f"  {key}: {value}")
    else:
        print("Strategy does not have an internal trade log or it's empty")

    # If the strategy has a trade log with entries, use it
    if strategy_has_trade_log and len(strategy_instance.trade_log) > 0:
        # Create a DataFrame from the trade log
        trades_df = pd.DataFrame(strategy_instance.trade_log)

        # Debug: Print the DataFrame to see what we're actually exporting
        # print("DEBUG - DataFrame from strategy trade log:")
        # print(trades_df[['Index', 'Exit Reason']].to_string())

        # Ensure the DataFrame has all the required columns
        required_columns = [
            "Index", "Entry DateTime", "Exit DateTime", "Entry Price", "Exit Price",
            "Profit Points", "Profit Percent", "Position", "Trade Duration", "Exit Reason"
        ]

        # Reorder columns to match the required format
        trades_df = trades_df[required_columns]

        # Debug: Print exit reasons after reordering
        #print("DEBUG - Exit reasons after reordering:")
        #print(trades_df['Exit Reason'].tolist())

        # Define the output file path
        output_dir = os.path.dirname(os.path.abspath(__file__))
        output_file = os.path.join(output_dir, "all_trades_outcome.csv")

        # Export to CSV
        trades_df.to_csv(output_file, index=False)
        print(f"Trade log exported to: {output_file} (using strategy's internal trade log)")
        print(f"Total trades exported: {len(trades_df)}")

        # Analyze exit reasons from the strategy's trade log
        exit_reason_counts = trades_df["Exit Reason"].value_counts()
        print("\n--- Exit Reason Analysis (from strategy trade log) ---")
        for reason, count in exit_reason_counts.items():
            percentage = (count / len(trades_df)) * 100
            print(f"{reason}: {count} trades ({percentage:.2f}%)")

        # Exit here to prevent the fallback code from running
    elif not strategy_has_trade_log or len(strategy_instance.trade_log) == 0:
        # If no trade log is available or it's empty, create one from the stats._trades DataFrame
        print("Using backtesting framework's trade data instead")
        all_trades = stats['_trades']
        if not all_trades.empty:
            # Create a new DataFrame with the required columns
            trades_list = []
            for idx, trade in all_trades.iterrows():
                entry_time = trade.EntryTime
                exit_time = trade.ExitTime
                entry_price = trade.EntryPrice
                exit_price = trade.ExitPrice
                # Get the raw PnL from the trade
                raw_pnl = trade.PnL
                position_type = "LONG" if trade.Size > 0 else "SHORT"
                duration = exit_time - entry_time

                # Calculate profit in points (price difference)
                if position_type == "LONG":
                    profit_points = exit_price - entry_price
                else:  # SHORT
                    profit_points = entry_price - exit_price

                # Calculate profit percent based on price difference
                profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0

                # Determine exit reason by comparing exit price with SL/TP
                exit_reason = "Unknown"

                # Check if SL and TP are set
                has_sl = hasattr(trade, 'SL') and not pd.isna(trade.SL)
                has_tp = hasattr(trade, 'TP') and not pd.isna(trade.TP)

                # Use a more realistic tolerance - 0.5% of price or minimum 5 points
                tolerance = max(exit_price * 0.005, 5.0)

                # Check if exit time is near market close (after 15:15)
                exit_time_str = exit_time.strftime('%H:%M')
                exit_date_str = exit_time.strftime('%Y-%m-%d')
                entry_date_str = entry_time.strftime('%Y-%m-%d')

                # Check for overnight exits (different dates and market open)
                if exit_date_str != entry_date_str and exit_time_str == "09:15":
                    exit_reason = "Overnight Exit"
                # Check for time-based exits (end of day)
                elif exit_time_str >= "15:15":
                    exit_reason = "Time-based Exit"
                elif has_sl and has_tp:
                    if position_type == "LONG":
                        # For LONG positions
                        # Check if price is near SL
                        if exit_price <= trade.SL + tolerance:
                            exit_reason = "Stop Loss"
                        # Check if price is near TP
                        elif exit_price >= trade.TP - tolerance:
                            exit_reason = "Take Profit"
                        # Check if price moved significantly toward SL
                        elif exit_price < entry_price and abs(exit_price - trade.SL) <= tolerance * 2:
                            exit_reason = "Likely Stop Loss"
                        # Check if price moved significantly toward TP
                        elif exit_price > entry_price and abs(exit_price - trade.TP) <= tolerance * 2:
                            exit_reason = "Likely Take Profit"
                        # If price moved down significantly from entry
                        elif exit_price < entry_price and (entry_price - exit_price) > tolerance * 2:
                            exit_reason = "Likely Stop Loss"
                        # If price moved up significantly from entry
                        elif exit_price > entry_price and (exit_price - entry_price) > tolerance * 2:
                            exit_reason = "Likely Take Profit"
                        else:
                            exit_reason = "Manual/Other Exit"
                    else:  # SHORT position
                        # For SHORT positions
                        # Check if price is near SL
                        if exit_price >= trade.SL - tolerance:
                            exit_reason = "Stop Loss"
                        # Check if price is near TP
                        elif exit_price <= trade.TP + tolerance:
                            exit_reason = "Take Profit"
                        # Check if price moved significantly toward SL
                        elif exit_price > entry_price and abs(exit_price - trade.SL) <= tolerance * 2:
                            exit_reason = "Likely Stop Loss"
                        # Check if price moved significantly toward TP
                        elif exit_price < entry_price and abs(exit_price - trade.TP) <= tolerance * 2:
                            exit_reason = "Likely Take Profit"
                        # If price moved up significantly from entry
                        elif exit_price > entry_price and (exit_price - entry_price) > tolerance * 2:
                            exit_reason = "Likely Stop Loss"
                        # If price moved down significantly from entry
                        elif exit_price < entry_price and (entry_price - exit_price) > tolerance * 2:
                            exit_reason = "Likely Take Profit"
                        else:
                            exit_reason = "Manual/Other Exit"
                        # Check if price moved significantly toward SL
                        if exit_price >= entry_price and abs(exit_price - trade.SL) <= tolerance * 2:
                            exit_reason = "Stop Loss"
                        # Check if price is near SL
                        elif exit_price >= trade.SL - tolerance:
                            exit_reason = "Stop Loss"
                        # Check if price is near TP
                        elif exit_price <= trade.TP + tolerance:
                            exit_reason = "Take Profit"
                        # Check if price moved significantly toward TP
                        elif exit_price <= entry_price and abs(exit_price - trade.TP) <= tolerance * 2:
                            exit_reason = "Take Profit"
                        # If price moved up significantly from entry, likely a stop loss
                        elif exit_price > entry_price and (exit_price - entry_price) > tolerance:
                            exit_reason = "Likely Stop Loss"
                        # If price moved down significantly from entry, likely a take profit
                        elif exit_price < entry_price and (entry_price - exit_price) > tolerance:
                            exit_reason = "Likely Take Profit"
                        else:
                            exit_reason = "Manual/Other Exit"
                elif has_sl:
                    # Only SL is set
                    if position_type == "LONG":
                        if exit_price <= trade.SL + tolerance:
                            exit_reason = "Stop Loss"
                        elif exit_price < entry_price and (entry_price - exit_price) > tolerance:
                            exit_reason = "Likely Stop Loss"
                        else:
                            exit_reason = "Manual/Other Exit"
                    else:  # SHORT
                        if exit_price >= trade.SL - tolerance:
                            exit_reason = "Stop Loss"
                        elif exit_price > entry_price and (exit_price - entry_price) > tolerance:
                            exit_reason = "Likely Stop Loss"
                        else:
                            exit_reason = "Manual/Other Exit"
                elif has_tp:
                    # Only TP is set
                    if position_type == "LONG":
                        if exit_price >= trade.TP - tolerance:
                            exit_reason = "Take Profit"
                        elif exit_price > entry_price and (exit_price - entry_price) > tolerance:
                            exit_reason = "Likely Take Profit"
                        else:
                            exit_reason = "Manual/Other Exit"
                    else:  # SHORT
                        if exit_price <= trade.TP + tolerance:
                            exit_reason = "Take Profit"
                        elif exit_price < entry_price and (entry_price - exit_price) > tolerance:
                            exit_reason = "Likely Take Profit"
                        else:
                            exit_reason = "Manual/Other Exit"

                trades_list.append({
                    "Index": idx + 1,
                    "Entry DateTime": entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Exit DateTime": exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Entry Price": round(entry_price, 2),
                    "Exit Price": round(exit_price, 2),
                    "Profit Points": round(profit_points, 2),
                    "Profit Percent": round(profit_percent, 2),
                    "Position": position_type,
                    "Trade Duration": str(duration),
                    "Exit Reason": exit_reason
                })

            # Create DataFrame and export to CSV
            trades_df = pd.DataFrame(trades_list)

            # Define the output file path
            output_dir = os.path.dirname(os.path.abspath(__file__))
            output_file = os.path.join(output_dir, "all_trades_outcome.csv")

            # Export to CSV
            trades_df.to_csv(output_file, index=False)
            print(f"Trade log exported to: {output_file}")
            print(f"Total trades exported: {len(trades_df)}")

            # Analyze exit reasons
            exit_reason_counts = trades_df["Exit Reason"].value_counts()

            print("\n--- Exit Reason Analysis ---")
            for reason, count in exit_reason_counts.items():
                percentage = (count / len(trades_df)) * 100
                print(f"{reason}: {count} trades ({percentage:.2f}%)")

            # Specifically check for overnight exits
            overnight_exits = trades_df[trades_df["Exit Reason"] == "Overnight Exit"]
            if len(overnight_exits) > 0:
                print(f"\nOvernight Exits: {len(overnight_exits)} trades")
                print("Sample overnight exits:")
                print(overnight_exits.head(3).to_string(index=False))
        else:
            print("No trades were executed, so no trade log is available to export.")
