import pandas as pd
import numpy as np
import yaml
import os
from backtesting import Strategy

def load_config(config_path="TrendAdaptiveSRRetestConfig.yaml"):
    """Load configuration from YAML file."""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")

    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)
    return config

def calculate_sma(series, period):
    """Calculate Simple Moving Average."""
    series = pd.Series(series)
    return series.rolling(period, min_periods=1).mean()

def calculate_atr(high, low, close, period):
    """Calculate Average True Range."""
    high = pd.Series(high)
    low = pd.Series(low)
    close = pd.Series(close)

    tr = pd.DataFrame(index=high.index)
    tr['hl'] = high - low
    tr['hc'] = abs(high - close.shift(1))
    tr['lc'] = abs(low - close.shift(1))
    true_range = tr[['hl', 'hc', 'lc']].max(axis=1)
    return true_range.ewm(span=period, adjust=False, min_periods=1).mean()

class TrendAdaptiveSRRetest(Strategy):
    """Trend Adaptive Support/Resistance Breakout and Retest Strategy with configuration support."""

    def init(self):
        """Initialize indicators and strategy variables."""
        # Load configuration
        config = load_config()
        strategy_config = config['strategy']

        # Set strategy parameters from config
        self.n_periods = strategy_config['n_periods']
        self.retest_window = strategy_config['retest_window']
        self.confirmation_lookback = strategy_config['confirmation_lookback']
        self.sl_atr_multiplier = strategy_config['sl_atr_multiplier']
        self.tp_rr = strategy_config['tp_rr']
        self.trend_ma_period = strategy_config['trend_ma_period']
        self.min_breakout_body_percent = strategy_config['min_breakout_body_percent']
        self.sr_band_percent = strategy_config['sr_band_percent']
        self.atr_period = strategy_config['atr_period']
        self.position_size = strategy_config['position_size']

        # Time constraints
        self.market_start_time = strategy_config['market_start_time']
        self.market_end_time = strategy_config['market_end_time']
        self.entry_start_time = strategy_config['entry_start_time']
        self.exit_end_time = strategy_config['exit_end_time']

        # Initialize indicators
        self.resistance = self.I(lambda x: pd.Series(x).rolling(self.n_periods, min_periods=1).max(), self.data.High, name="resistance")
        self.support = self.I(lambda x: pd.Series(x).rolling(self.n_periods, min_periods=1).min(), self.data.Low, name="support")
        self.atr = self.I(calculate_atr, self.data.High, self.data.Low, self.data.Close, self.atr_period, name="atr")
        self.trend_ma = self.I(calculate_sma, self.data.Close, self.trend_ma_period, name="trend_ma")

        # Variables for trade logging
        self.trade_log = []
        self.current_trade_entry_details = {} # To temporarily store entry info
        self.trade_index_counter = 1 # To assign unique index to each trade

        # Existing strategy variables
        self.breakout_up_level = np.nan
        self.breakout_down_level = np.nan
        self.breakout_candle_idx = -1
        self.retest_touch_candle_idx = -1
        self.retest_touch_candle_high = np.nan
        self.retest_touch_candle_low = np.nan
        self.waiting_for_confirmation = False
        self.breakout_direction = 0

    def next(self):
        current_idx = len(self.data.Close) - 1
        current_datetime = self.data.index[-1] # Get current bar's datetime
        current_close = self.data.Close[-1]
        current_open = self.data.Open[-1]
        current_high = self.data.High[-1]
        current_low = self.data.Low[-1]

        # Get current time in HH:MM format
        current_time = current_datetime.strftime('%H:%M')

        # --- Time-based Exit Logic ---
        # Close all positions if we're near market close
        if self.position and current_time >= self.exit_end_time:
            # Store exit reason before closing position
            if self.current_trade_entry_details:
                self.current_trade_entry_details["Exit Reason"] = "Time-based Exit"
            self.position.close()

        # --- Trade Exit Logging (Before processing new entries) ---
        if self.current_trade_entry_details and not self.position:
            self._record_exit_details(current_datetime, current_close)
            self.current_trade_entry_details = {}

        if current_idx < 1:
            return

        # Skip entry logic if outside trading hours
        if current_time < self.entry_start_time or current_time >= self.exit_end_time:
            return

        previous_close = self.data.Close[-2]

        current_resistance_level = self.resistance[-2]
        current_support_level = self.support[-2]

        if np.isnan(current_resistance_level) or np.isnan(current_support_level):
            return

        res_band_upper = current_resistance_level * (1 + self.sr_band_percent)
        res_band_lower = current_resistance_level * (1 - self.sr_band_percent)
        sup_band_upper = current_support_level * (1 + self.sr_band_percent)
        sup_band_lower = current_support_level * (1 - self.sr_band_percent)

        if np.isnan(self.trend_ma[-1]) or np.isnan(self.trend_ma[-2]):
            return

        is_uptrend = current_close > self.trend_ma[-1] and self.trend_ma[-1] > self.trend_ma[-2]
        is_downtrend = current_close < self.trend_ma[-1] and self.trend_ma[-1] < self.trend_ma[-2]

        if self.breakout_candle_idx != -1 and current_idx - self.breakout_candle_idx > self.retest_window * 2:
            self._reset_breakout_state() # This calls _reset_retest_state internally

        # Upward breakout
        if previous_close <= current_resistance_level and current_close > current_resistance_level:
            if current_close - max(current_open, current_resistance_level) >= (current_high - current_low) * self.min_breakout_body_percent \
               and is_uptrend:
                self.breakout_up_level = current_resistance_level
                self.breakout_down_level = np.nan
                self.breakout_direction = 1
                self.breakout_candle_idx = current_idx
                self._reset_retest_state() # Ensure retest state is clean for new breakout

        # Downward breakout
        elif previous_close >= current_support_level and current_close < current_support_level:
            if min(current_open, current_support_level) - current_close >= (current_high - current_low) * self.min_breakout_body_percent \
               and is_downtrend:
                self.breakout_down_level = current_support_level
                self.breakout_up_level = np.nan
                self.breakout_direction = -1
                self.breakout_candle_idx = current_idx
                self._reset_retest_state() # Ensure retest state is clean for new breakout

        # --- Retest Confirmation & Entry Logic ---
        if not self.position:
            # Long Entry
            if self.breakout_direction == 1 and self.breakout_candle_idx != -1 and \
               current_idx > self.breakout_candle_idx and \
               current_idx <= self.breakout_candle_idx + self.retest_window:

                if not self.waiting_for_confirmation and \
                   (current_low <= self.breakout_up_level <= current_high or \
                    (current_low <= res_band_upper and current_high >= res_band_lower)):
                    self.retest_touch_candle_idx = current_idx
                    self.retest_touch_candle_high = current_high
                    self.retest_touch_candle_low = current_low
                    self.waiting_for_confirmation = True

                if self.waiting_for_confirmation and \
                   (current_idx == self.retest_touch_candle_idx + self.confirmation_lookback or current_idx == self.retest_touch_candle_idx + 1):
                    if current_close > self.retest_touch_candle_high:

                        if np.isnan(self.atr[-1]):
                            self._reset_retest_state()
                            return

                        sl = self.retest_touch_candle_low - (self.atr[-1] * self.sl_atr_multiplier)
                        sl = min(sl, self.retest_touch_candle_low)

                        if sl >= current_close:
                             self._reset_retest_state()
                             return

                        tp = current_close + (current_close - sl) * self.tp_rr

                        self.buy(sl=sl, tp=tp, size=self.position_size)
                        self._record_entry_details(
                            entry_type="LONG",
                            entry_datetime=current_datetime,
                            entry_price=current_close,
                            sl_price=sl,
                            tp_price=tp
                        )
                        self._reset_breakout_state()

            # Short Entry
            elif self.breakout_direction == -1 and self.breakout_candle_idx != -1 and \
                 current_idx > self.breakout_candle_idx and \
                 current_idx <= self.breakout_candle_idx + self.retest_window:

                if not self.waiting_for_confirmation and \
                   (current_high >= self.breakout_down_level >= current_low or \
                    (current_high >= sup_band_lower and current_low <= sup_band_upper)):
                    self.retest_touch_candle_idx = current_idx
                    self.retest_touch_candle_high = current_high
                    self.retest_touch_candle_low = current_low
                    self.waiting_for_confirmation = True

                if self.waiting_for_confirmation and \
                   (current_idx == self.retest_touch_candle_idx + self.confirmation_lookback or current_idx == self.retest_touch_candle_idx + 1):
                    if current_close < self.retest_touch_candle_low:

                        if np.isnan(self.atr[-1]):
                            self._reset_retest_state()
                            return

                        sl = self.retest_touch_candle_high + (self.atr[-1] * self.sl_atr_multiplier)
                        sl = max(sl, self.retest_touch_candle_high)

                        if sl <= current_close:
                            self._reset_retest_state()
                            return

                        tp = current_close - (sl - current_close) * self.tp_rr

                        self.sell(sl=sl, tp=tp, size=self.position_size)
                        self._record_entry_details(
                            entry_type="SHORT",
                            entry_datetime=current_datetime,
                            entry_price=current_close,
                            sl_price=sl,
                            tp_price=tp
                        )
                        self._reset_breakout_state()
            else:
                # If waiting for confirmation but conditions aren't met within the lookback, reset
                if self.waiting_for_confirmation and \
                   current_idx > self.retest_touch_candle_idx + self.confirmation_lookback + 1:
                    self._reset_retest_state()

    def _record_entry_details(self, entry_type, entry_datetime, entry_price, sl_price, tp_price):
        """Records the entry details of a new trade."""
        self.current_trade_entry_details = {
            "Index": self.trade_index_counter,
            "Entry DateTime": entry_datetime,
            "Entry Price": entry_price,
            "Position": entry_type,
            "SL Price": sl_price,
            "TP Price": tp_price
        }
        self.trade_index_counter += 1

    def _record_exit_details(self, exit_datetime, exit_price):
        """Records the exit details for the currently open trade."""
        if not self.current_trade_entry_details:
            return

        entry_type = self.current_trade_entry_details["Position"]
        entry_price = self.current_trade_entry_details["Entry Price"]
        sl_price = self.current_trade_entry_details["SL Price"]
        tp_price = self.current_trade_entry_details["TP Price"]

        profit_points = exit_price - entry_price if entry_type == "LONG" else entry_price - exit_price

        # Determine exit reason (simplified logic)
        if "Exit Reason" in self.current_trade_entry_details:
            exit_reason = self.current_trade_entry_details["Exit Reason"]
        else:
            tolerance = 0.01  # 1% tolerance for price matching
            exit_time = exit_datetime.strftime('%H:%M')

            if exit_time >= self.exit_end_time:
                exit_reason = "Time-based Exit"
            elif entry_type == "LONG":
                if abs(exit_price - sl_price) <= tolerance:
                    exit_reason = "Stop Loss"
                elif abs(exit_price - tp_price) <= tolerance:
                    exit_reason = "Take Profit"
                else:
                    exit_reason = "Breakout Window Expiry"
            else:  # SHORT
                if abs(exit_price - sl_price) <= tolerance:
                    exit_reason = "Stop Loss"
                elif abs(exit_price - tp_price) <= tolerance:
                    exit_reason = "Take Profit"
                else:
                    exit_reason = "Breakout Window Expiry"

        duration = exit_datetime - self.current_trade_entry_details["Entry DateTime"]
        profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0

        self.trade_log.append({
            "Index": self.current_trade_entry_details["Index"],
            "Entry DateTime": self.current_trade_entry_details["Entry DateTime"].strftime('%Y-%m-%d %H:%M:%S'),
            "Exit DateTime": exit_datetime.strftime('%Y-%m-%d %H:%M:%S'),
            "Entry Price": round(entry_price, 2),
            "Exit Price": round(exit_price, 2),
            "Exit Reason": exit_reason,
            "Profit Points": round(profit_points, 2),
            "Profit Percent": round(profit_percent, 2),
            "Position": entry_type,
            "Trade Duration": str(duration)
        })

    def _reset_breakout_state(self):
        """Resets all breakout-related state variables."""
        self.breakout_up_level = np.nan
        self.breakout_down_level = np.nan
        self.breakout_direction = 0
        self.breakout_candle_idx = -1
        self._reset_retest_state() # Calls the retest state reset

    def _reset_retest_state(self):
        """Resets all retest-related state variables."""
        self.retest_touch_candle_idx = -1
        self.retest_touch_candle_high = np.nan
        self.retest_touch_candle_low = np.nan
        self.waiting_for_confirmation = False

    def close_all_trades(self):
        """
        This method will be called by Backtest after the backtest is finished,
        to ensure any open positions are closed and logged.
        """
        if self.position and self.current_trade_entry_details:
            last_close = self.data.Close[-1]
            last_datetime = self.data.index[-1]
            self._record_exit_details(last_datetime, last_close)
            self.current_trade_entry_details = {}