#!/usr/bin/env python3
"""
Test script for the enhanced take profit strategy
"""

import pandas as pd
import numpy as np
from backtesting import Backtest
from strategies import SupportResistanceBreakoutRetestImprovedCSV
import warnings
warnings.filterwarnings('ignore')

def load_test_data():
    """Load real NIFTY 1-minute data for testing"""
    try:
        # Load the real NIFTY data
        data_path = r"C:\Users\<USER>\Desktop\Python\data\NIFTY_1min.csv"
        data = pd.read_csv(data_path, parse_dates=['date'])
        
        # Set the date column as index
        data.set_index('date', inplace=True)
        
        # Ensure column names match what the strategy expects
        # The file has lowercase column names, but strategy might expect capitalized
        data.columns = [col.capitalize() for col in data.columns]
        
        # Make sure we have all required columns
        required_columns = ['Open', 'High', 'Low', 'Close']
        for col in required_columns:
            if col not in data.columns:
                raise ValueError(f"Required column {col} not found in dataset")
        
        print(f"Loaded NIFTY data: {len(data)} rows from {data.index.min()} to {data.index.max()}")
        return data
    except Exception as e:
        print(f"Error loading data: {e}")
        raise

def test_enhanced_strategy(start_date=None, end_date=None):
    """Test the enhanced strategy with different configurations
    
    Args:
        start_date (str, optional): Start date for backtest in 'YYYY-MM-DD' format
        end_date (str, optional): End date for backtest in 'YYYY-MM-DD' format
    """
    
    print("=" * 60)
    print("TESTING ENHANCED TAKE PROFIT STRATEGY ON FULL NIFTY DATASET")
    print("=" * 60)
    
    # Load data
    data = load_test_data()
    
    # Filter data by date range if specified
    if start_date:
        data = data[data.index >= start_date]
        print(f"Filtered data from {start_date}")
    if end_date:
        data = data[data.index <= end_date]
        print(f"Filtered data to {end_date}")
    
    print(f"Using {len(data)} data points for backtest")
    
    # Test configurations
    configs = [
        {
            'name': 'Original Configuration',
            'extended_breakout_window': 6,  # Original short window
            'dynamic_tp_enabled': False,
            'trailing_stop_enabled': False,
            'partial_tp_enabled': False,
            'tp_rr': 2
        },
        {
            'name': 'Extended Window Only',
            'extended_breakout_window': 20,
            'dynamic_tp_enabled': False,
            'trailing_stop_enabled': False,
            'partial_tp_enabled': False,
            'tp_rr': 2
        },
        {
            'name': 'Full Enhanced Strategy',
            'extended_breakout_window': 20,
            'dynamic_tp_enabled': True,
            'trailing_stop_enabled': True,
            'partial_tp_enabled': True,
            'tp_rr': 2
        }
    ]
    
    results = []
    
    for config in configs:
        print(f"\nTesting: {config['name']}")
        print("-" * 40)
        
        # Create strategy class with configuration
        class TestStrategy(SupportResistanceBreakoutRetestImprovedCSV):
            extended_breakout_window = config['extended_breakout_window']
            dynamic_tp_enabled = config['dynamic_tp_enabled']
            trailing_stop_enabled = config['trailing_stop_enabled']
            partial_tp_enabled = config['partial_tp_enabled']
            tp_rr = config['tp_rr']
            debug_mode = False  # Disable debug for cleaner output
        
        try:
            # Run backtest
            bt = Backtest(
                data,
                TestStrategy,
                cash=30000,
                margin=1/10,
                commission=0.0,
                trade_on_close=True,
                hedging=False
            )
            
            result = bt.run()
            
            # Get trade statistics
            trades = result._trades
            if len(trades) > 0:
                # Calculate exit reason distribution
                exit_reasons = {}
                for trade in trades.itertuples():
                    # This is a simplified exit reason calculation
                    # In real implementation, we'd get this from the strategy's trade log
                    if hasattr(trade, 'ReturnPct'):
                        if trade.ReturnPct > 0.01:  # > 1% profit
                            reason = "Take Profit"
                        elif trade.ReturnPct < -0.01:  # > 1% loss
                            reason = "Stop Loss"
                        else:
                            reason = "Other Exit"
                        
                        exit_reasons[reason] = exit_reasons.get(reason, 0) + 1
                
                total_trades = len(trades)
                win_rate = len(trades[trades['ReturnPct'] > 0]) / total_trades * 100
                avg_return = trades['ReturnPct'].mean() * 100
                
                print(f"Total Trades: {total_trades}")
                print(f"Win Rate: {win_rate:.1f}%")
                print(f"Average Return: {avg_return:.2f}%")
                print(f"Total Return: {result['Return [%]']:.2f}%")
                print(f"Max Drawdown: {result['Max. Drawdown [%]']:.2f}%")
                print(f"Sharpe Ratio: {result['Sharpe Ratio']:.2f}")
                print(f"Calmar Ratio: {result['Calmar Ratio']:.2f}")
                print(f"Sortino Ratio: {result.get('Sortino Ratio', 'N/A')}")
                print(f"Avg. Trade Duration: {result.get('Avg. Trade Duration', 'N/A')}")
                
                if exit_reasons:
                    print("\nExit Reason Distribution:")
                    for reason, count in exit_reasons.items():
                        pct = count / total_trades * 100
                        print(f"  {reason}: {count} trades ({pct:.1f}%)")
                
                results.append({
                    'config': config['name'],
                    'total_trades': total_trades,
                    'win_rate': win_rate,
                    'total_return': result['Return [%]'],
                    'max_drawdown': result['Max. Drawdown [%]'],
                    'take_profit_pct': exit_reasons.get('Take Profit', 0) / total_trades * 100 if total_trades > 0 else 0,
                    'sharpe_ratio': result['Sharpe Ratio'],
                    'calmar_ratio': result['Calmar Ratio'],
                    'sortino_ratio': result.get('Sortino Ratio', 'N/A')
                })
            else:
                print("No trades generated")
                results.append({
                    'config': config['name'],
                    'total_trades': 0,
                    'win_rate': 0,
                    'total_return': 0,
                    'max_drawdown': 0,
                    'take_profit_pct': 0,
                    'sharpe_ratio': 0,
                    'calmar_ratio': 0,
                    'sortino_ratio': 0
                })
                
        except Exception as e:
            print(f"Error running backtest: {e}")
            results.append({
                'config': config['name'],
                'error': str(e)
            })
    
    # Summary comparison
    print("\n" + "=" * 60)
    print("SUMMARY COMPARISON")
    print("=" * 60)
    
    for result in results:
        if 'error' not in result:
            print(f"\n{result['config']}:")
            print(f"  Total Trades: {result['total_trades']}")
            print(f"  Win Rate: {result['win_rate']:.1f}%")
            print(f"  Total Return: {result['total_return']:.2f}%")
            print(f"  Max Drawdown: {result['max_drawdown']:.2f}%")
            print(f"  Take Profit %: {result['take_profit_pct']:.1f}%")
            print(f"  Sharpe Ratio: {result['sharpe_ratio']:.2f}")
            print(f"  Calmar Ratio: {result['calmar_ratio']:.2f}")
            if isinstance(result['sortino_ratio'], (int, float)):
                print(f"  Sortino Ratio: {result['sortino_ratio']:.2f}")
            else:
                print(f"  Sortino Ratio: {result['sortino_ratio']}")
        else:
            print(f"\n{result['config']}: ERROR - {result['error']}")

if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run backtest on NIFTY data')
    parser.add_argument('--start', type=str, help='Start date in YYYY-MM-DD format')
    parser.add_argument('--end', type=str, help='End date in YYYY-MM-DD format')
    
    args = parser.parse_args()
    
    # Run the backtest with optional date range
    test_enhanced_strategy(start_date=args.start, end_date=args.end)
