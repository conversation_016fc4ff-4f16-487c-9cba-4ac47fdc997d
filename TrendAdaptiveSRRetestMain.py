import pandas as pd
import numpy as np
import os
import yaml
from backtesting import Backtest
from TrendAdaptiveSRRetest import TrendAdaptiveSRRetest, load_config

def load_and_process_data(config):
    """Load and process data according to configuration."""
    data_config = config['data']

    try:
        data = pd.read_csv(
            data_config['file_path'],
            parse_dates=[data_config['date_column']] if data_config['parse_dates'] else None,
            index_col=data_config['index_column'] if data_config['index_column'] else None
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {data_config['file_path']}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming)
    column_map_lower = {col.lower(): col for col in data.columns}

    rename_dict = {}
    # Map standard OHLC names first
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        else:
            # Check if the correctly cased 'bt_name' (e.g. 'Open') already exists
            if bt_name not in data.columns:
                 print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
                 exit()

    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present
    if 'volume' in column_map_lower:
        # If 'volume' (lowercase) exists, rename its original cased version to 'Volume'
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns: # Check if 'Volume' (exact case) is already present
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0

    # Verify final column names
    expected_bt_cols = data_config['required_columns']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")

    # Apply date filtering if configured
    if data_config.get('start_date') and data_config.get('end_date'):
        data = data.loc[data_config['start_date']:data_config['end_date']]
        print(f"Data filtered for testing. New shape: {data.shape}")

    return data

def run_backtest(config, data):
    """Run backtest with configuration."""
    backtest_config = config['backtest']

    # Initialize and run the backtest
    bt = Backtest(
        data,
        TrendAdaptiveSRRetest,
        cash=backtest_config['cash'],
        margin=backtest_config['margin'],
        commission=backtest_config['commission']
    )

    print("Running backtest...")
    stats = bt.run()
    print("\n--- Backtest Performance Report ---")
    print(stats)

    return bt, stats

def print_detailed_metrics(stats, config):
    """Print detailed performance metrics."""
    if not config['logging']['enable_detailed_metrics']:
        return

    print(f"\n--- Detailed Metrics ---")
    print(f"Total Return (%):                 {stats['Return [%]']:.2f}%")
    if 'Return (Ann.) [%]' in stats:
        print(f"Annualized Return (%):            {stats['Return (Ann.) [%]']:.2f}%")
    else:
        print(f"Annualized Return (%):            N/A (duration < 1 year or not calculated)")
    print(f"Max Drawdown (%):                 {stats['Max. Drawdown [%]']:.2f}%")
    print(f"Sharpe Ratio (annualized):        {stats['Sharpe Ratio']:.2f}")
    print(f"Sortino Ratio (annualized):       {stats['Sortino Ratio']:.2f}")
    print(f"Win Rate (%):                     {stats['Win Rate [%]']:.2f}%")
    print(f"Profit Factor:                    {stats['Profit Factor']:.2f}")
    print(f"Total Number of Trades:           {stats['# Trades']}")

def export_trade_log(bt, stats, config):
    """Export trade log to CSV."""
    print("\n--- Exporting Trade Log to CSV ---")

    strategy_instance = bt._strategy
    output_config = config['output']

    # Check if strategy has internal trade log
    if hasattr(strategy_instance, 'trade_log') and strategy_instance.trade_log:
        print(f"Strategy has internal trade log with {len(strategy_instance.trade_log)} entries")

        # Create DataFrame from trade log
        trades_df = pd.DataFrame(strategy_instance.trade_log)

        # Reorder columns to match required format
        trades_df = trades_df[output_config['required_trade_columns']]

        # Export to CSV
        output_file = output_config['trade_log_file']
        trades_df.to_csv(output_file, index=False)
        print(f"Trade log exported to: {output_file} (using strategy's internal trade log)")
        print(f"Total trades exported: {len(trades_df)}")
    else:
        # Use backtesting framework's trade data as fallback
        print("Using backtesting framework's trade data instead")
        all_trades = stats['_trades']
        if not all_trades.empty:
            trades_list = []
            for idx, trade in all_trades.iterrows():
                entry_time = trade.EntryTime
                exit_time = trade.ExitTime
                entry_price = trade.EntryPrice
                exit_price = trade.ExitPrice
                position_type = "LONG" if trade.Size > 0 else "SHORT"
                duration = exit_time - entry_time

                # Calculate profit in points
                if position_type == "LONG":
                    profit_points = exit_price - entry_price
                else:  # SHORT
                    profit_points = entry_price - exit_price

                profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0

                # Simplified exit reason determination
                exit_time_str = exit_time.strftime('%H:%M')
                if exit_time_str >= "15:15":
                    exit_reason = "Time-based Exit"
                elif hasattr(trade, 'SL') and not pd.isna(trade.SL):
                    if (position_type == "LONG" and exit_price <= trade.SL * 1.01) or \
                       (position_type == "SHORT" and exit_price >= trade.SL * 0.99):
                        exit_reason = "Stop Loss"
                    else:
                        exit_reason = "Breakout Window Expiry"
                elif hasattr(trade, 'TP') and not pd.isna(trade.TP):
                    if (position_type == "LONG" and exit_price >= trade.TP * 0.99) or \
                       (position_type == "SHORT" and exit_price <= trade.TP * 1.01):
                        exit_reason = "Take Profit"
                    else:
                        exit_reason = "Breakout Window Expiry"
                else:
                    exit_reason = "Breakout Window Expiry"

                trades_list.append({
                    "Index": idx + 1,
                    "Entry DateTime": entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Exit DateTime": exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Entry Price": round(entry_price, 2),
                    "Exit Price": round(exit_price, 2),
                    "Profit Points": round(profit_points, 2),
                    "Profit Percent": round(profit_percent, 2),
                    "Position": position_type,
                    "Trade Duration": str(duration),
                    "Exit Reason": exit_reason
                })

            # Create DataFrame and export to CSV
            trades_df = pd.DataFrame(trades_list)
            output_file = output_config['trade_log_file']
            trades_df.to_csv(output_file, index=False)
            print(f"Trade log exported to: {output_file}")
            print(f"Total trades exported: {len(trades_df)}")
        else:
            print("No trades were executed, so no trade log is available to export.")

def main():
    """Main execution function."""
    # Load configuration
    config = load_config()

    # Load and process data
    data = load_and_process_data(config)

    # Run backtest
    bt, stats = run_backtest(config, data)

    # Print detailed metrics
    print_detailed_metrics(stats, config)

    # Export trade log
    export_trade_log(bt, stats, config)

if __name__ == '__main__':
    main()
