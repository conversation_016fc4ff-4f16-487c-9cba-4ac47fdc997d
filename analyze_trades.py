import pandas as pd
import numpy as np

# Load the trades data
trades_df = pd.read_csv('all_trades_outcome.csv')

# Display the data
print("Trade Data:")
print(trades_df)
print("\n")

# Calculate basic statistics
total_trades = len(trades_df)
print(f"Total number of trades: {total_trades}")

# Calculate average trade PnL
avg_trade_pnl = trades_df['Profit Points'].mean()
print(f"Average Trade PnL (points): {avg_trade_pnl:.2f}")

# Separate winning and losing trades
winning_trades = trades_df[trades_df['Profit Points'] > 0]
losing_trades = trades_df[trades_df['Profit Points'] < 0]

# Count winning and losing trades
num_winning_trades = len(winning_trades)
num_losing_trades = len(losing_trades)
win_rate = (num_winning_trades / total_trades) * 100

print(f"Number of winning trades: {num_winning_trades} ({win_rate:.2f}%)")
print(f"Number of losing trades: {num_losing_trades} ({100-win_rate:.2f}%)")

# Calculate average winning and losing trade values
if num_winning_trades > 0:
    avg_winning_trade = winning_trades['Profit Points'].mean()
    print(f"Average Winning Trade (points): {avg_winning_trade:.2f}")
else:
    print("No winning trades found.")

if num_losing_trades > 0:
    avg_losing_trade = losing_trades['Profit Points'].mean()
    print(f"Average Losing Trade (points): {avg_losing_trade:.2f}")
else:
    print("No losing trades found.")

# Calculate total profit and loss
total_profit = winning_trades['Profit Points'].sum()
total_loss = losing_trades['Profit Points'].sum()
net_profit = total_profit + total_loss

print(f"\nTotal profit from winning trades: {total_profit:.2f}")
print(f"Total loss from losing trades: {total_loss:.2f}")
print(f"Net profit: {net_profit:.2f}")

# Calculate profit factor
if total_loss != 0:
    profit_factor = abs(total_profit / total_loss)
    print(f"Profit Factor: {profit_factor:.2f}")
else:
    print("Profit Factor: Infinite (no losing trades)")

# Analyze exit reasons
exit_reasons = trades_df['Exit Reason'].value_counts()
print("\nExit Reason Analysis:")
for reason, count in exit_reasons.items():
    percentage = (count / total_trades) * 100
    print(f"{reason}: {count} trades ({percentage:.2f}%)")

# Check for inconsistencies
print("\nChecking for inconsistencies:")

# Check for winning trades marked as Take Profit
winning_tp = winning_trades[winning_trades['Exit Reason'] == 'Take Profit']
if len(winning_tp) > 0:
    print(f"Found {len(winning_tp)} winning trades marked as Take Profit")
else:
    print("No winning trades marked as Take Profit")

# Check for losing trades marked as Take Profit
losing_tp = losing_trades[losing_trades['Exit Reason'] == 'Take Profit']
if len(losing_tp) > 0:
    print(f"Found {len(losing_tp)} losing trades marked as Take Profit")
    print("These trades have negative profit but are marked as Take Profit:")
    print(losing_tp[['Index', 'Position', 'Entry Price', 'Exit Price', 'Profit Points', 'Exit Reason']])
else:
    print("No losing trades marked as Take Profit")

# Check for winning trades marked as Stop Loss
winning_sl = winning_trades[winning_trades['Exit Reason'] == 'Stop Loss']
if len(winning_sl) > 0:
    print(f"\nFound {len(winning_sl)} winning trades marked as Stop Loss")
    print("These trades have positive profit but are marked as Stop Loss:")
    print(winning_sl[['Index', 'Position', 'Entry Price', 'Exit Price', 'Profit Points', 'Exit Reason']])
else:
    print("\nNo winning trades marked as Stop Loss")

# Check for losing trades marked as Stop Loss
losing_sl = losing_trades[losing_trades['Exit Reason'] == 'Stop Loss']
if len(losing_sl) > 0:
    print(f"Found {len(losing_sl)} losing trades marked as Stop Loss")
else:
    print("No losing trades marked as Stop Loss")

# Verify if the reported statistics match our calculations
print("\nVerifying reported statistics:")
reported_avg_pnl = 225.51
reported_avg_winning = 747.54
reported_avg_losing = -296.53

print(f"Reported Average Trade PnL: {reported_avg_pnl:.2f}, Our calculation: {avg_trade_pnl:.2f}")
if num_winning_trades > 0:
    print(f"Reported Average Winning Trade: {reported_avg_winning:.2f}, Our calculation: {avg_winning_trade:.2f}")
if num_losing_trades > 0:
    print(f"Reported Average Losing Trade: {reported_avg_losing:.2f}, Our calculation: {avg_losing_trade:.2f}")

# Calculate what values would be needed to match the reported statistics
print("\nCalculating what would be needed to match reported statistics:")

# For average trade PnL
total_pnl_needed = reported_avg_pnl * total_trades
current_total_pnl = trades_df['Profit Points'].sum()
missing_pnl = total_pnl_needed - current_total_pnl
print(f"Current total PnL: {current_total_pnl:.2f}")
print(f"Total PnL needed for reported average: {total_pnl_needed:.2f}")
print(f"Missing PnL: {missing_pnl:.2f}")

# For average winning trade
if num_winning_trades > 0:
    total_winning_pnl_needed = reported_avg_winning * num_winning_trades
    current_winning_pnl = winning_trades['Profit Points'].sum()
    missing_winning_pnl = total_winning_pnl_needed - current_winning_pnl
    print(f"\nCurrent total winning PnL: {current_winning_pnl:.2f}")
    print(f"Total winning PnL needed for reported average: {total_winning_pnl_needed:.2f}")
    print(f"Missing winning PnL: {missing_winning_pnl:.2f}")

# For average losing trade
if num_losing_trades > 0:
    total_losing_pnl_needed = reported_avg_losing * num_losing_trades
    current_losing_pnl = losing_trades['Profit Points'].sum()
    missing_losing_pnl = total_losing_pnl_needed - current_losing_pnl
    print(f"\nCurrent total losing PnL: {current_losing_pnl:.2f}")
    print(f"Total losing PnL needed for reported average: {total_losing_pnl_needed:.2f}")
    print(f"Missing losing PnL: {missing_losing_pnl:.2f}")

# Check if there's a possibility of missing trades
print("\nPossibility of missing trades:")
if total_trades < 10:
    print(f"The report mentions 10 trades, but we only found {total_trades} in the CSV.")
elif total_trades == 10 and (abs(missing_pnl) > 0.01 or (num_winning_trades > 0 and abs(missing_winning_pnl) > 0.01) or (num_losing_trades > 0 and abs(missing_losing_pnl) > 0.01)):
    print("The number of trades matches (10), but the PnL values don't match the reported averages.")
    print("This suggests either:")
    print("1. The reported statistics are incorrect")
    print("2. The CSV data doesn't match what was used to calculate the statistics")
    print("3. There's a different calculation method being used")
else:
    print("The number of trades and PnL values appear to be consistent with the reported statistics.")