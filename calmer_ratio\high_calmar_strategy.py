import pandas as pd
import numpy as np
import datetime as dt
import csv
import matplotlib.pyplot as plt
from backtesting import Backtest, Strategy
from backtesting.test import SMA

# ===== INDICATOR FUNCTIONS =====

def ATR(high, low, close, n=14):
    """
    Calculate Average True Range (ATR)
    """
    tr1 = pd.Series(high) - pd.Series(low)
    tr2 = abs(pd.Series(high) - pd.Series(close).shift(1))
    tr3 = abs(pd.Series(low) - pd.Series(close).shift(1))
    
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(n).mean().values
    
    return atr

def BB(array, n, std_dev):
    """
    Calculate Bollinger Bands
    """
    # Calculate the middle band (SMA)
    middle = pd.Series(array).rolling(n).mean().values
    
    # Calculate the standard deviation
    std = pd.Series(array).rolling(n).std().values
    
    # Calculate the upper and lower bands
    upper = middle + std_dev * std
    lower = middle - std_dev * std
    
    return upper, middle, lower

def RSI(array, n=14):
    """
    Calculate Relative Strength Index (RSI)
    """
    # Convert to pandas series for easier calculation
    close_delta = pd.Series(array).diff()
    
    # Make two series: one for gains and one for losses
    up = close_delta.clip(lower=0)
    down = -1 * close_delta.clip(upper=0)
    
    # Calculate the EWMA (Exponential Weighted Moving Average)
    ma_up = up.ewm(com=n-1, adjust=True, min_periods=n).mean()
    ma_down = down.ewm(com=n-1, adjust=True, min_periods=n).mean()
    
    # Calculate the RSI
    rs = ma_up / ma_down
    rsi = 100 - (100 / (1 + rs))
    
    return rsi.values

def is_time_between(time_str, start_time_str, end_time_str):
    """
    Check if a time string is between start and end time strings
    """
    time = dt.datetime.strptime(time_str, "%H:%M").time()
    start_time = dt.datetime.strptime(start_time_str, "%H:%M").time()
    end_time = dt.datetime.strptime(end_time_str, "%H:%M").time()
    return start_time <= time <= end_time

# ===== STRATEGY CLASS =====

class HighCalmarStrategy(Strategy):
    """
    Enhanced strategy focused on achieving high Calmar Ratio through:
    1. Advanced mean reversion with volatility-adjusted entries/exits
    2. Robust risk management with adaptive stop losses
    3. Time-based constraints for intraday trading
    4. Daily loss limits to preserve capital
    """
    
    # Strategy parameters (optimizable)
    fast_ma = 5            # Fast moving average period
    slow_ma = 20           # Slow moving average period
    bb_window = 10         # Bollinger Band period - shorter for intraday
    bb_std = 1.5           # Bollinger Band standard deviation - tighter for more signals
    atr_period = 10        # ATR period for volatility measurement - shorter for intraday
    rsi_period = 7         # RSI period - shorter for intraday
    rsi_oversold = 40      # RSI oversold threshold - less extreme for more signals
    rsi_overbought = 60    # RSI overbought threshold - less extreme for more signals
    
    # Risk management parameters
    stop_loss_atr = 1.0    # Stop loss at 1.0x ATR - even tighter for better risk management
    take_profit_atr = 2.5  # Take profit at 2.5x ATR - wider for better reward/risk ratio
    max_daily_loss = 1.5   # 1.5% maximum daily loss - more conservative
    max_trades_per_day = 5 # Maximum trades per day - reduced to focus on quality over quantity
    
    # Time constraints
    market_start_time = "09:15"
    market_end_time = "15:30"
    entry_start_time = "09:30"  # Allow time for indicators to stabilize
    exit_end_time = "15:15"     # Force close all trades before market close
    
    # Trade tracking
    trade_records = []
    current_trade = None
    daily_pnl = 0.0
    daily_trade_count = 0
    current_day = None
    
    def init(self):
        """Initialize strategy indicators and variables"""
        # Price-based indicators
        self.fast_ma_line = self.I(SMA, self.data.Close, self.fast_ma)
        self.slow_ma_line = self.I(SMA, self.data.Close, self.slow_ma)
        self.bb_upper, self.bb_mid, self.bb_lower = self.I(BB, self.data.Close, self.bb_window, self.bb_std)
        self.atr = self.I(ATR, self.data.High, self.data.Low, self.data.Close, self.atr_period)
        self.rsi = self.I(RSI, self.data.Close, self.rsi_period)
        
        # Initialize trade tracking
        self.trade_records = []
        self.current_trade = None
        self.daily_pnl = 0.0
        self.daily_trade_count = 0
        self.current_day = None
        
        # Store stop loss and take profit levels
        self.stop_loss = None
        self.take_profit = None
        
        print(f"Strategy initialized with parameters:")
        print(f"  Moving Averages: Fast={self.fast_ma}, Slow={self.slow_ma}")
        print(f"  Bollinger Bands: Window={self.bb_window}, StdDev={self.bb_std}")
        print(f"  RSI: Period={self.rsi_period}, Oversold={self.rsi_oversold}, Overbought={self.rsi_overbought}")
        print(f"  Trading Window: {self.entry_start_time} to {self.exit_end_time}")
    
    def next(self):
        """Main strategy logic executed on each bar"""
        # Skip trading until we have enough data for all indicators
        if (np.isnan(self.bb_lower[-1]) or np.isnan(self.bb_upper[-1]) or 
            np.isnan(self.atr[-1]) or np.isnan(self.rsi[-1]) or 
            np.isnan(self.fast_ma_line[-1]) or np.isnan(self.slow_ma_line[-1])):
            return
        
        # Get current time and date information
        current_datetime = self.data.index[-1]
        current_time = current_datetime.strftime("%H:%M")
        current_day = current_datetime.date()
        
        # Reset daily counters if a new day has started
        if self.current_day is None or current_day != self.current_day:
            self.daily_pnl = 0.0
            self.daily_trade_count = 0
            self.current_day = current_day
            print(f"New trading day: {current_day}")
        
        # Force close all positions at exit_end_time
        if is_time_between(current_time, self.exit_end_time, self.market_end_time) and self.position:
            if self.current_trade:
                self.current_trade["exit_reason"] = "EOD_CLOSE"
            self.position.close()
            print(f"EOD close at {current_time}")
            return
        
        # Check for stop loss or take profit if in a position
        if self.position:
            current_price = self.data.Close[-1]
            
            # Check stop loss
            if (self.position.is_long and current_price <= self.stop_loss) or \
               (self.position.is_short and current_price >= self.stop_loss):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "STOP_LOSS"
                self.position.close()
                print(f"Stop loss triggered at {current_time}, price: {current_price:.2f}")
                return
            
            # Check take profit
            if (self.position.is_long and current_price >= self.take_profit) or \
               (self.position.is_short and current_price <= self.take_profit):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "TAKE_PROFIT"
                self.position.close()
                print(f"Take profit triggered at {current_time}, price: {current_price:.2f}")
                return
        
        # Only enter new positions between entry_start_time and exit_end_time
        # and if we haven't hit daily limits
        if not is_time_between(current_time, self.entry_start_time, self.exit_end_time):
            return
        
        # Check if we've hit daily loss limit or max trades per day
        if self.daily_pnl <= -self.max_daily_loss or self.daily_trade_count >= self.max_trades_per_day:
            return
        
        # Trading logic - only enter if not in a position
        if not self.position:
            # Get current ATR value for stop loss and take profit calculation
            atr_value = self.atr[-1]
            
            # LONG ENTRY CONDITIONS - Combination of signals (need at least 2 out of 3):
            # 1. Fast MA crosses above Slow MA (trend signal)
            # 2. Price is near lower Bollinger Band (mean reversion signal)
            # 3. RSI is below oversold threshold (momentum signal)
            long_signals = 0
            ma_crossover = self.fast_ma_line[-2] <= self.slow_ma_line[-2] and self.fast_ma_line[-1] > self.slow_ma_line[-1]
            price_near_bb = self.data.Close[-1] <= self.bb_lower[-1] * 1.03  # Tighter buffer
            rsi_oversold = self.rsi[-1] < self.rsi_oversold
            
            # Prioritize certain combinations
            if ma_crossover:
                long_signals += 1
            if price_near_bb:
                long_signals += 1
            if rsi_oversold:
                long_signals += 1
                
            # Additional filter: Only enter if price is trending up in the very short term
            short_term_trend = self.data.Close[-1] > self.data.Close[-3]
                
            if long_signals >= 2 and short_term_trend and not self.position:
                entry_price = self.data.Close[-1]
                
                # Calculate stop loss and take profit levels based on ATR
                self.stop_loss = entry_price - (atr_value * self.stop_loss_atr)
                self.take_profit = entry_price + (atr_value * self.take_profit_atr)
                
                # Enter long position
                self.buy()
                self.daily_trade_count += 1
                print(f"LONG entry at {current_time}, price: {entry_price:.2f}, stop: {self.stop_loss:.2f}, target: {self.take_profit:.2f}")
                
                # Record trade information
                self.current_trade = {
                    "entry_datetime": current_datetime,
                    "entry_price": entry_price,
                    "position": "LONG",
                    "stop_loss": self.stop_loss,
                    "take_profit": self.take_profit,
                    "exit_reason": "OPEN"
                }
            
            # SHORT ENTRY CONDITIONS - Combination of signals (need at least 2 out of 3):
            # 1. Fast MA crosses below Slow MA (trend signal)
            # 2. Price is near upper Bollinger Band (mean reversion signal)
            # 3. RSI is above overbought threshold (momentum signal)
            short_signals = 0
            ma_crossover = self.fast_ma_line[-2] >= self.slow_ma_line[-2] and self.fast_ma_line[-1] < self.slow_ma_line[-1]
            price_near_bb = self.data.Close[-1] >= self.bb_upper[-1] * 0.97  # Tighter buffer
            rsi_overbought = self.rsi[-1] > self.rsi_overbought
            
            # Prioritize certain combinations
            if ma_crossover:
                short_signals += 1
            if price_near_bb:
                short_signals += 1
            if rsi_overbought:
                short_signals += 1
                
            # Additional filter: Only enter if price is trending down in the very short term
            short_term_trend = self.data.Close[-1] < self.data.Close[-3]
                
            if short_signals >= 2 and short_term_trend and not self.position:
                
                entry_price = self.data.Close[-1]
                
                # Calculate stop loss and take profit levels based on ATR
                self.stop_loss = entry_price + (atr_value * self.stop_loss_atr)
                self.take_profit = entry_price - (atr_value * self.take_profit_atr)
                
                # Enter short position
                self.sell()
                self.daily_trade_count += 1
                print(f"SHORT entry at {current_time}, price: {entry_price:.2f}, stop: {self.stop_loss:.2f}, target: {self.take_profit:.2f}")
                
                # Record trade information
                self.current_trade = {
                    "entry_datetime": current_datetime,
                    "entry_price": entry_price,
                    "position": "SHORT",
                    "stop_loss": self.stop_loss,
                    "take_profit": self.take_profit,
                    "exit_reason": "OPEN"
                }
    
    def on_trade(self, trade):
        """Called when a trade is closed"""
        if self.current_trade is not None:
            # Calculate trade metrics
            exit_datetime = self.data.index[-1]
            exit_price = trade.exit_price
            exit_reason = self.current_trade.get("exit_reason", "UNKNOWN")
            
            # Calculate profit
            profit_points = exit_price - self.current_trade["entry_price"] if self.current_trade["position"] == "LONG" else self.current_trade["entry_price"] - exit_price
            profit_percent = (profit_points / self.current_trade["entry_price"]) * 100
            
            # Update daily P&L tracking
            self.daily_pnl += profit_percent / 100  # Convert percentage to decimal
            
            # Calculate trade duration
            duration = exit_datetime - self.current_trade["entry_datetime"]
            duration_str = str(duration)
            
            # Create trade record
            trade_record = {
                "index": len(self.trade_records) + 1,
                "entry_datetime": self.current_trade["entry_datetime"],
                "exit_datetime": exit_datetime,
                "entry_price": self.current_trade["entry_price"],
                "exit_price": exit_price,
                "stop_loss": self.current_trade["stop_loss"],
                "take_profit": self.current_trade["take_profit"],
                "exit_reason": exit_reason,
                "profit_points": profit_points,
                "profit_percent": profit_percent,
                "position": self.current_trade["position"],
                "trade_duration": duration_str,
                "exit_reason": exit_reason
            }
            
            # Add to trade records
            self.trade_records.append(trade_record)
            print(f"Trade closed: {self.current_trade['position']} {exit_reason} P/L: {profit_percent:.2f}%")
            
            # Reset current trade
            self.current_trade = None
            self.stop_loss = None
            self.take_profit = None

# ===== MAIN EXECUTION =====

if __name__ == '__main__':
    # Load the data
    file_path = 'C:\\Users\\<USER>\\Desktop\\Python\\data\\NIFTY_1min.csv'
    try:
        data = pd.read_csv(
            file_path,
            parse_dates=['date'],
            index_col='date'
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming)
    column_map_lower = {col.lower(): col for col in data.columns}
    
    rename_dict = {}
    # Map standard OHLC names
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        else:
            # Check if the correctly cased name already exists
            if bt_name not in data.columns:
                 print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
                 exit()
            
    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present
    if 'volume' in column_map_lower:
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns:
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0
    
    # Verify final column names
    expected_bt_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")
    
    # Filter data for testing - adjust date range as needed
    # For initial testing, use a smaller dataset
    data = data.loc['2015-01-01':'2015-01-31']
    print(f"Data filtered for testing. New shape: {data.shape}")
    
    # Add slippage and commission for realistic modeling
    commission = 0.0005  # 0.05% commission per trade
    
    # Create and run backtest
    bt = Backtest(data, HighCalmarStrategy, cash=100000, commission=commission)
    stats = bt.run()

    # Print the stats
    print("\n===== PERFORMANCE METRICS =====")
    print(stats[['Start', 'End', 'Duration', 'Exposure Time [%]',
                'Equity Final [$]', 'Equity Peak [$]', 'Return [%]',
                'Buy & Hold Return [%]', 'Max. Drawdown [%]', '# Trades',
                'Win Rate [%]', 'Best Trade [%]', 'Worst Trade [%]',
                'Avg. Trade [%]', 'Max. Trade Duration', 'Avg. Trade Duration']])
    
    # Calculate and print Calmar Ratio
    equity = stats['_equity_curve']['Equity']
    returns = equity.pct_change().dropna()
    drawdown = 1 - equity / equity.cummax()
    max_drawdown = drawdown.max()
    annual_return = returns.mean() * 252  # Assuming 252 trading days in a year
    calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else float('inf')
    
    print(f"\n===== RISK-ADJUSTED METRICS =====")
    print(f"Annualized Return: {annual_return:.4f}")
    print(f"Maximum Drawdown: {max_drawdown:.4f}")
    print(f"Calmar Ratio: {calmar_ratio:.4f}")
    print(f"Sharpe Ratio: {stats['Sharpe Ratio']:.4f}")
    print(f"Sortino Ratio: {stats['Sortino Ratio']:.4f}")
    
    # Save trade records to CSV
    strategy_instance = bt._strategy
    if hasattr(strategy_instance, 'trade_records') and strategy_instance.trade_records:
        output_file = 'C:\\Users\\<USER>\\Desktop\\Python\\SimpleStrategies\\high_calmar_trade_records.csv'
        
        # Create a list to store processed trade records
        trade_records = []
        
        # Process each trade from our custom records
        for trade in strategy_instance.trade_records:
            trade_record = {
                'Index': trade['index'],
                'Entry DateTime': trade['entry_datetime'],
                'Exit DateTime': trade['exit_datetime'],
                'Entry Price': trade['entry_price'],
                'Exit Price': trade['exit_price'],
                'Exit Reason': trade['exit_reason'],
                'Profit Points': trade['profit_points'],
                'Profit Percent': trade['profit_percent'],
                'Position': trade['position'],
                'Trade Duration': trade['trade_duration'],
                'Exit Reason': trade['exit_reason']  # Duplicate field as requested
            }
            
            trade_records.append(trade_record)
        
        # Define CSV headers
        headers = [
            'Index', 'Entry DateTime', 'Exit DateTime', 'Entry Price', 'Exit Price',
            'Exit Reason', 'Profit Points', 'Profit Percent', 'Position', 'Trade Duration', 'Exit Reason'
        ]
        
        # Write to CSV
        with open(output_file, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            
            for trade in trade_records:
                writer.writerow(trade)
        
        print(f"\nSaved {len(trade_records)} trade records to {output_file}")
    else:
        print("\nNo trades were executed during the backtest.")
    
    # Plot the equity curve and drawdowns
    plt.figure(figsize=(12, 8))
    
    # Plot equity curve
    plt.subplot(2, 1, 1)
    plt.plot(equity)
    plt.title('Equity Curve')
    plt.grid(True)
    
    # Plot drawdowns
    plt.subplot(2, 1, 2)
    plt.fill_between(drawdown.index, drawdown.values)
    plt.title('Drawdowns')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('C:\\Users\\<USER>\\Desktop\\Python\\SimpleStrategies\\high_calmar_equity_curve.png')
    print("Equity curve and drawdowns plot saved to high_calmar_equity_curve.png")
    
    # Show the interactive plot
    bt.plot()