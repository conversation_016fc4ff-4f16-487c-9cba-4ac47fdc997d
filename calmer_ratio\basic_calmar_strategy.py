import pandas as pd
import numpy as np
import datetime as dt
import csv
from backtesting import Backtest, Strategy
from backtesting.test import SM<PERSON>

def is_time_between(time_str, start_time_str, end_time_str):
    """Check if a time string is between start and end time strings."""
    time = dt.datetime.strptime(time_str, "%H:%M").time()
    start_time = dt.datetime.strptime(start_time_str, "%H:%M").time()
    end_time = dt.datetime.strptime(end_time_str, "%H:%M").time()
    return start_time <= time <= end_time

class BasicCalmarStrategy(Strategy):
    """
    A simple intraday strategy focused on achieving high Calmar Ratio
    using moving average crossovers with strict risk management.
    """
    
    # Strategy parameters
    fast_ma = 5
    slow_ma = 20
    
    # Risk management parameters
    stop_loss_pct = 0.5  # 0.5% stop loss
    take_profit_pct = 0.5  # 0.5% take profit
    
    # Time constraints
    market_start_time = "09:15"
    market_end_time = "15:30"
    entry_start_time = "09:30"
    exit_end_time = "15:15"  # force close all trades
    
    # Trade tracking
    trade_records = []
    current_trade = None
    
    def init(self):
        """Initialize strategy indicators and variables"""
        # Calculate moving averages
        self.fast = self.I(SMA, self.data.Close, self.fast_ma)
        self.slow = self.I(SMA, self.data.Close, self.slow_ma)
        
        # Initialize trade tracking
        self.trade_records = []
        self.current_trade = None
        
        # Store stop loss and take profit levels
        self.stop_loss = None
        self.take_profit = None
        
        # Print some debug info
        print(f"Strategy initialized with fast_ma={self.fast_ma}, slow_ma={self.slow_ma}")
        print(f"Trading window: {self.entry_start_time} to {self.exit_end_time}")
    
    def next(self):
        """Main strategy logic executed on each bar"""
        # Skip trading until we have enough data
        if np.isnan(self.fast[-1]) or np.isnan(self.slow[-1]):
            return
        
        # Get current time
        current_datetime = self.data.index[-1]
        current_time = current_datetime.strftime("%H:%M")
        
        # Debug output every 1000 bars
        if len(self.data) % 1000 == 0:
            print(f"Processing bar {len(self.data)}, time: {current_time}, fast_ma: {self.fast[-1]:.2f}, slow_ma: {self.slow[-1]:.2f}")
        
        # Force close all positions at exit_end_time
        if is_time_between(current_time, self.exit_end_time, self.market_end_time) and self.position:
            if self.current_trade:
                self.current_trade["exit_reason"] = "EOD_CLOSE"
            self.position.close()
            print(f"EOD close at {current_time}")
            return
        
        # Only enter new positions between entry_start_time and exit_end_time
        if not is_time_between(current_time, self.entry_start_time, self.exit_end_time):
            return
        
        # Check for stop loss or take profit if in a position
        if self.position:
            current_price = self.data.Close[-1]
            
            # Check stop loss
            if (self.position.is_long and current_price <= self.stop_loss) or \
               (self.position.is_short and current_price >= self.stop_loss):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "STOP_LOSS"
                self.position.close()
                print(f"Stop loss triggered at {current_time}, price: {current_price:.2f}")
                return
            
            # Check take profit
            if (self.position.is_long and current_price >= self.take_profit) or \
               (self.position.is_short and current_price <= self.take_profit):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "TAKE_PROFIT"
                self.position.close()
                print(f"Take profit triggered at {current_time}, price: {current_price:.2f}")
                return
        
        # Trading logic - only enter if not in a position
        if not self.position:
            # Simple moving average crossover strategy
            # Buy when fast MA crosses above slow MA
            if self.fast[-2] <= self.slow[-2] and self.fast[-1] > self.slow[-1]:
                entry_price = self.data.Close[-1]
                
                # Calculate stop loss and take profit levels
                self.stop_loss = entry_price * (1 - self.stop_loss_pct/100)
                self.take_profit = entry_price * (1 + self.take_profit_pct/100)
                
                # Enter long position
                self.buy()
                print(f"LONG entry at {current_time}, price: {entry_price:.2f}, stop: {self.stop_loss:.2f}, target: {self.take_profit:.2f}")
                
                # Record trade information
                self.current_trade = {
                    "entry_datetime": current_datetime,
                    "entry_price": entry_price,
                    "position": "LONG",
                    "stop_loss": self.stop_loss,
                    "take_profit": self.take_profit,
                    "exit_reason": "OPEN"
                }
            
            # Sell when fast MA crosses below slow MA
            elif self.fast[-2] >= self.slow[-2] and self.fast[-1] < self.slow[-1]:
                entry_price = self.data.Close[-1]
                
                # Calculate stop loss and take profit levels
                self.stop_loss = entry_price * (1 + self.stop_loss_pct/100)
                self.take_profit = entry_price * (1 - self.take_profit_pct/100)
                
                # Enter short position
                self.sell()
                print(f"SHORT entry at {current_time}, price: {entry_price:.2f}, stop: {self.stop_loss:.2f}, target: {self.take_profit:.2f}")
                
                # Record trade information
                self.current_trade = {
                    "entry_datetime": current_datetime,
                    "entry_price": entry_price,
                    "position": "SHORT",
                    "stop_loss": self.stop_loss,
                    "take_profit": self.take_profit,
                    "exit_reason": "OPEN"
                }
    
    def on_trade(self, trade):
        """Called when a trade is closed"""
        if self.current_trade is not None:
            # Calculate trade metrics
            exit_datetime = self.data.index[-1]
            exit_price = trade.exit_price
            exit_reason = self.current_trade.get("exit_reason", "UNKNOWN")
            
            # Calculate profit
            profit_points = exit_price - self.current_trade["entry_price"] if self.current_trade["position"] == "LONG" else self.current_trade["entry_price"] - exit_price
            profit_percent = (profit_points / self.current_trade["entry_price"]) * 100
            
            # Calculate trade duration
            duration = exit_datetime - self.current_trade["entry_datetime"]
            duration_str = str(duration)
            
            # Create trade record
            trade_record = {
                "index": len(self.trade_records) + 1,
                "entry_datetime": self.current_trade["entry_datetime"],
                "exit_datetime": exit_datetime,
                "entry_price": self.current_trade["entry_price"],
                "exit_price": exit_price,
                "stop_loss": self.current_trade["stop_loss"],
                "take_profit": self.current_trade.get("take_profit"),
                "exit_reason": exit_reason,
                "profit_points": profit_points,
                "profit_percent": profit_percent,
                "position": self.current_trade["position"],
                "trade_duration": duration_str
            }
            
            # Add to trade records
            self.trade_records.append(trade_record)
            print(f"Trade closed: {self.current_trade['position']} {exit_reason} P/L: {profit_percent:.2f}%")
            
            # Reset current trade
            self.current_trade = None
            self.stop_loss = None
            self.take_profit = None

# --- Main Execution ---
if __name__ == '__main__':
    # Load the data
    file_path = 'C:\\Users\\<USER>\\Desktop\\Python\\data\\NIFTY_1min.csv'
    try:
        data = pd.read_csv(
            file_path,
            parse_dates=['date'],
            index_col='date'
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming)
    column_map_lower = {col.lower(): col for col in data.columns}
    
    rename_dict = {}
    # Map standard OHLC names
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        else:
            # Check if the correctly cased name already exists
            if bt_name not in data.columns:
                 print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
                 exit()
            
    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present
    if 'volume' in column_map_lower:
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns:
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0
    
    # Verify final column names
    expected_bt_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")
    
    # Filter data for testing - use just 1 week of data for faster testing
    data = data.loc['2015-01-01':'2015-01-07']
    print(f"Data filtered for testing. New shape: {data.shape}")
    
    # Add slippage and commission for realistic modeling
    commission = 0.0005  # 0.05% commission per trade
    
    # Create and run backtest
    bt = Backtest(data, BasicCalmarStrategy, cash=100000, commission=commission)
    stats = bt.run()

    # Print the stats
    print("\n===== PERFORMANCE METRICS =====")
    print(stats[['Start', 'End', 'Duration', 'Exposure Time [%]',
                'Equity Final [$]', 'Equity Peak [$]', 'Return [%]',
                'Buy & Hold Return [%]', 'Max. Drawdown [%]', '# Trades',
                'Win Rate [%]', 'Best Trade [%]', 'Worst Trade [%]',
                'Avg. Trade [%]', 'Max. Trade Duration', 'Avg. Trade Duration']])
    
    # Calculate and print Calmar Ratio
    equity = stats['_equity_curve']['Equity']
    returns = equity.pct_change().dropna()
    drawdown = 1 - equity / equity.cummax()
    max_drawdown = drawdown.max()
    annual_return = returns.mean() * 252  # Assuming 252 trading days in a year
    calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else float('inf')
    
    print(f"\n===== RISK-ADJUSTED METRICS =====")
    print(f"Annualized Return: {annual_return:.4f}")
    print(f"Maximum Drawdown: {max_drawdown:.4f}")
    print(f"Calmar Ratio: {calmar_ratio:.4f}")
    print(f"Sharpe Ratio: {stats['Sharpe Ratio']:.4f}")
    print(f"Sortino Ratio: {stats['Sortino Ratio']:.4f}")
    
    # Save trade records to CSV
    strategy_instance = bt._strategy
    if hasattr(strategy_instance, 'trade_records') and strategy_instance.trade_records:
        output_file = 'C:\\Users\\<USER>\\Desktop\\Python\\SimpleStrategies\\basic_trade_records.csv'
        
        # Create a list to store processed trade records
        trade_records = []
        
        # Process each trade from our custom records
        for trade in strategy_instance.trade_records:
            trade_record = {
                'Index': trade['index'],
                'Entry DateTime': trade['entry_datetime'],
                'Exit DateTime': trade['exit_datetime'],
                'Entry Price': trade['entry_price'],
                'Exit Price': trade['exit_price'],
                'Exit Reason': trade['exit_reason'],
                'Profit Points': trade['profit_points'],
                'Profit Percent': trade['profit_percent'],
                'Position': trade['position'],
                'Trade Duration': trade['trade_duration'],
                'Exit Reason': trade['exit_reason']  # Duplicate field as requested
            }
            
            trade_records.append(trade_record)
        
        # Define CSV headers
        headers = [
            'Index', 'Entry DateTime', 'Exit DateTime', 'Entry Price', 'Exit Price',
            'Exit Reason', 'Profit Points', 'Profit Percent', 'Position', 'Trade Duration', 'Exit Reason'
        ]
        
        # Write to CSV
        with open(output_file, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            
            for trade in trade_records:
                writer.writerow(trade)
        
        print(f"\nSaved {len(trade_records)} trade records to {output_file}")
    else:
        print("\nNo trades were executed during the backtest.")
    
    # Plot the results
    bt.plot()