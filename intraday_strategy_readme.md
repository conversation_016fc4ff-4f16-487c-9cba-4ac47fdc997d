# Intraday Nifty Trading Algorithm

## Overview

This document describes an intraday trading algorithm designed for the Nifty index, implemented in Python using the `backtesting.py` library. The strategy aims to execute a minimum of one and a maximum of five trades per day, with all positions squared off by the end of the trading session. Trades are sequential, meaning a new trade is initiated only after the preceding one is closed. The entry price is the Nifty index value at the moment of trade initiation. The algorithm does not use volume data or require initial capital configuration for its core logic.

## Strategy Logic

1.  **Trading Hours**: The strategy operates within defined market hours (e.g., 9:15 AM to 3:15 PM).
2.  **Daily Trade Limits**:
    *   A maximum of 5 trades are allowed per day.
    *   The strategy targets at least 1 trade per day, contingent on signal generation.
3.  **Sequential Trading**: Only one position (long or short) is held at any time. A new trade can only be initiated after the current trade is closed.
4.  **Entry Signal**: The current implementation uses a simple Simple Moving Average (SMA) crossover for illustrative purposes:
    *   **Long Entry**: Short-term SMA (e.g., 10-period) crosses above a long-term SMA (e.g., 30-period).
    *   **Short Entry**: Short-term SMA crosses below a long-term SMA.
    *   The entry price is the Nifty `Close` price of the candle on which the signal occurs.
5.  **Trade Management**:
    *   **Take Profit**: A fixed point target (e.g., 20 Nifty points).
    *   **Stop Loss**: A fixed point stop-loss (e.g., 10 Nifty points).
    *   **Trade Size**: Fixed at 1 unit.
6.  **Intraday Square Off**: All open positions are automatically squared off a few minutes before the market closes (e.g., at 3:15 PM if market closes at 3:30 PM).
7.  **No Volume Data**: The strategy does not use volume for its decisions.
8.  **No Capital Configuration for Logic**: The core trading logic (entry/exit signals, trade management) does not depend on a pre-defined capital amount. `backtesting.py` uses a default cash amount for simulation, but the strategy's trade sizing is fixed (1 unit).

## Files

1.  `intraday_nifty_algo.py`: The Python script containing the strategy implementation and backtesting setup.
2.  `sample_nifty_intraday_data.csv`: A sample CSV file with Nifty 1-minute data.
3.  `intraday_nifty_trade_log.csv`: Generated by the script, this CSV file contains a detailed log of all trades executed during the backtest.
4.  `requirements.txt`: Lists the necessary Python libraries.

## Setup and Execution

1.  **Prerequisites**:
    *   Python 3.x
    *   Ensure you have the libraries listed in `requirements.txt` installed. If you don't have `requirements.txt` or it's incomplete, you'll need:
        ```bash
        pip install pandas numpy backtesting.py
        ```
        (Note: `backtesting.py` is installed as `Backtesting` via pip).

2.  **Data File**:
    *   Prepare your Nifty 1-minute (or other frequency) data in a CSV file.
    *   The CSV must have the following columns: `date`, `open`, `high`, `low`, `close`.
    *   The `date` column should be parsable by pandas (e.g., `YYYY-MM-DD HH:MM:SS`).
    *   **Create `sample_nifty_intraday_data.csv` with the following content if you want to run the sample:**
        ```csv
        date,open,high,low,close
        2023-01-02 09:15:00,18000.50,18010.25,17990.75,18005.10
        2023-01-02 09:16:00,18005.10,18015.60,18002.30,18012.50
        2023-01-02 09:17:00,18012.50,18018.00,18010.15,18015.30
        2023-01-02 09:18:00,18015.30,18022.75,18014.50,18020.00
        2023-01-02 09:19:00,18020.00,18020.50,18010.80,18011.20
        2023-01-02 09:20:00,18011.20,18015.90,18005.25,18008.60
        2023-01-02 15:28:00,18050.00,18055.00,18048.50,18052.30
        2023-01-02 15:29:00,18052.30,18054.75,18050.10,18053.00
        2023-01-03 09:15:00,18060.10,18070.50,18055.20,18065.70
        2023-01-03 09:16:00,18065.70,18075.00,18063.80,18072.10
        2023-01-03 09:17:00,18072.10,18072.10,18060.50,18061.00
        2023-01-03 09:18:00,18061.00,18065.25,18058.90,18063.40
        2023-01-03 15:28:00,18100.00,18105.00,18098.50,18102.30
        2023-01-03 15:29:00,18102.30,18104.75,18100.10,18103.00
        ```
    *   Place this file (e.g., `sample_nifty_intraday_data.csv` or your own data file) in the same directory as `intraday_nifty_algo.py`, or update the `data_file` variable in the script to point to its location.

3.  **Run the Script**:
    ```bash
    python intraday_nifty_algo.py
    ```

## Output

1.  **Console Output**: The script will print:
    *   Confirmation of data loading.
    *   A performance report from `backtesting.py` (e.g., Return, Sharpe Ratio, Max Drawdown, Win Rate, etc.).
    *   Confirmation that the trade log has been saved.
    *   A printout of the trade log DataFrame.

2.  **`intraday_nifty_trade_log.csv`**: This file will be generated in the same directory and will contain the trade log with the following columns:
    *   `Index`: A running index for the trades.
    *   `Entry DateTime`: Timestamp of trade entry.
    *   `Exit DateTime`: Timestamp of trade exit.
    *   `Entry Price`: Price at which the trade was entered.
    *   `Exit Price`: Price at which the trade was exited.
    *   `Exit Reason`: Reason for trade exit (e.g., "TakeProfit", "StopLoss", "Market Close Square Off").
    *   `Profit Points`: Profit or loss in Nifty points.
    *   `Profit Percent`: Profit or loss as a percentage of the entry price.
    *   `Position`: "LONG" or "SHORT".

## Customization

*   **Strategy Parameters**: Modify `max_trades_per_day`, `target_points`, `stop_loss_points`, `market_open_time`, `market_close_time`, and SMA periods (`sma_short`, `sma_long`) within the `IntradayNiftyAlgo` class in `intraday_nifty_algo.py` to suit your requirements.
*   **Entry/Exit Logic**: The current SMA crossover is a basic example. You can replace the logic within the `next()` method to implement more sophisticated entry and exit signals.
*   **Data Source**: Change the `data_file` variable in the `if __name__ == '__main__':` block to use your own data.
*   **Backtest Settings**: Adjust `cash` and `commission` in the `Backtest(...)` call if needed, though the strategy logic itself is designed to be capital-agnostic for sizing.

This algorithm provides a framework for intraday trading on Nifty. Remember that past performance is not indicative of future results, and thorough testing and risk management are crucial before deploying any trading strategy with real capital.